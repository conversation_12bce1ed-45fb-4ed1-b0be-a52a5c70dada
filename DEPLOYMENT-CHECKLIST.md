# 📋 Shared Hosting Deployment Checklist

## Before Deployment

### ✅ Configuration
- [ ] Update `src/config/production.ts` with your Supabase service role key
- [ ] Verify Supabase URL and anon key are correct
- [ ] Test the build locally: `npm run build`
- [ ] Test the preview: `npm run preview`

### ✅ Files Ready
- [ ] `dist` folder contains all built files
- [ ] `.htaccess` file is in the `dist` folder
- [ ] All assets are properly bundled

## Deployment Steps

### ✅ Upload Files
- [ ] Login to cPanel or FTP
- [ ] Navigate to `public_html` (or your domain folder)
- [ ] **IMPORTANT:** Backup any existing files
- [ ] Delete old files (if any)
- [ ] Upload ALL contents from `dist` folder
- [ ] Verify `.htaccess` file is uploaded
- [ ] Check file permissions (755 for folders, 644 for files)

### ✅ Domain Configuration
- [ ] Domain points to correct folder
- [ ] SSL certificate is active
- [ ] DNS is properly configured

## Post-Deployment Testing

### ✅ Basic Functionality
- [ ] Homepage loads correctly
- [ ] Menu page displays items
- [ ] About page works
- [ ] Contact page works
- [ ] Language switching works (TR/EN)

### ✅ Admin Panel
- [ ] Admin login page loads: `yoursite.com/admin`
- [ ] Database test shows connection success
- [ ] Menu management works (add/edit/delete)
- [ ] Turkish translations display correctly

### ✅ Mobile & Performance
- [ ] Site works on mobile devices
- [ ] Images load properly
- [ ] Page load speed is acceptable
- [ ] All links work correctly

## Troubleshooting

### Common Issues:
1. **404 errors on page refresh**
   - Check if `.htaccess` file is uploaded
   - Verify Apache mod_rewrite is enabled

2. **Database connection fails**
   - Check Supabase credentials in `production.ts`
   - Verify Supabase project is active

3. **Images not loading**
   - Check if image URLs are accessible
   - Verify CORS settings

4. **Admin panel not working**
   - Check browser console for errors
   - Verify service role key is correct

## Security Notes
- [ ] Never expose service role key in client-side code
- [ ] Set up proper RLS policies in Supabase
- [ ] Consider adding basic auth for admin panel
- [ ] Regular backups of database

## Performance Optimization
- [ ] Enable gzip compression (included in .htaccess)
- [ ] Set up caching headers (included in .htaccess)
- [ ] Consider CDN for images
- [ ] Monitor site performance

---

## 🎉 Deployment Complete!

Your Shebo's Burger website is now live with:
- ✅ Full Turkish localization
- ✅ Admin panel for menu management
- ✅ Responsive design
- ✅ Database integration
- ✅ SEO-friendly URLs
