# 🔧 React Router Routing Fix Guide

## The Problem
When you refresh a page or navigate directly to a URL like `/menu` or `/privacy-policy`, you get a 404 error. This happens because:

1. **Development mode**: Vite dev server doesn't handle client-side routing by default
2. **Production mode**: Web servers try to find actual files/folders for these routes
3. **React Router**: Uses client-side routing, so all routes should serve `index.html`

## The Solution

### ✅ For Development (Vite Dev Server)
Updated `vite.config.ts` with:
```typescript
server: {
  historyApiFallback: {
    index: '/index.html'
  }
}
```

### ✅ For Production Deployment

#### 1. **Apache Servers** (Most shared hosting)
- File: `public/.htaccess` ✅ **INCLUDED**
- Automatically copied to `dist/` folder during build
- Handles all routing + compression + caching

#### 2. **Netlify/Vercel-like platforms**
- File: `public/_redirects` ✅ **INCLUDED**
- Simple redirect rule: `/* /index.html 200`

#### 3. **IIS Servers** (Windows hosting)
- File: `public/web.config` ✅ **INCLUDED**
- XML configuration for IIS URL rewriting

## 🚀 How to Test

### Development Mode:
1. Run: `npm run dev`
2. Go to: `http://localhost:5173/menu`
3. Refresh the page - should work ✅
4. Use browser back/forward - should work ✅

### Production Mode:
1. Run: `npm run build`
2. Run: `npm run preview`
3. Go to: `http://localhost:4173/menu`
4. Refresh the page - should work ✅

### On Your Server:
1. Upload the entire `dist/` folder contents to your web root
2. Make sure `.htaccess` file is uploaded (check with FTP client)
3. Test all routes by refreshing pages

## 🔍 Troubleshooting

### If it still doesn't work:

1. **Check .htaccess file exists**:
   ```bash
   ls -la dist/.htaccess
   ```

2. **Verify Apache mod_rewrite is enabled** (ask your hosting provider)

3. **Check server error logs** for any .htaccess syntax errors

4. **Try the simple .htaccess version**:
   ```apache
   RewriteEngine On
   RewriteCond %{REQUEST_FILENAME} !-f
   RewriteCond %{REQUEST_FILENAME} !-d
   RewriteRule . /index.html [L]
   ```

5. **For Nginx servers**, add this to server config:
   ```nginx
   location / {
     try_files $uri $uri/ /index.html;
   }
   ```

## 📁 Files Included in Build

After running `npm run build`, your `dist/` folder contains:
- ✅ `index.html` - Main app file
- ✅ `.htaccess` - Apache routing rules
- ✅ `_redirects` - Netlify/Vercel rules  
- ✅ `web.config` - IIS rules
- ✅ `assets/` - CSS, JS, and other assets

## 🎯 Next Steps

1. **Test locally**: Use `npm run preview` to test production build
2. **Deploy**: Upload `dist/` folder contents to your web server
3. **Verify**: Test all routes work with refresh and direct navigation
4. **Monitor**: Check server logs for any routing issues

The routing should now work perfectly on all hosting platforms! 🚀
