-- Add multi-language support to existing tables
-- Run this in your Supabase SQL Editor
-- This script only adds language columns and copies existing content, no new data

-- Add language columns to existing tables (if they don't have them)

-- Add language columns to blog_posts table
ALTER TABLE blog_posts 
ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr',
ADD COLUMN IF NOT EXISTS title_en TEXT,
ADD COLUMN IF NOT EXISTS excerpt_en TEXT,
ADD COLUMN IF NOT EXISTS content_en TEXT;

-- Add language columns to menu_items table
ALTER TABLE menu_items 
ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr',
ADD COLUMN IF NOT EXISTS name_en TEXT,
ADD COLUMN IF NOT EXISTS description_en TEXT;

-- Add language columns to content table (if it exists)
-- If content table doesn't exist, create it
CREATE TABLE IF NOT EXISTS content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    image_url TEXT,
    display_order INTEGER DEFAULT 0,
    section VARCHAR(255) NOT NULL,
    page VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add language columns to content table
ALTER TABLE content 
ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr',
ADD COLUMN IF NOT EXISTS title_en TEXT,
ADD COLUMN IF NOT EXISTS content_en TEXT;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_language ON blog_posts(language);
CREATE INDEX IF NOT EXISTS idx_menu_items_language ON menu_items(language);
CREATE INDEX IF NOT EXISTS idx_content_language ON content(language);
CREATE INDEX IF NOT EXISTS idx_content_page_section ON content(page, section);
CREATE INDEX IF NOT EXISTS idx_menu_items_category ON menu_items(category);
CREATE INDEX IF NOT EXISTS idx_menu_items_type ON menu_items(menu_type);

-- Update existing data to have Turkish as default language
UPDATE blog_posts SET language = 'tr' WHERE language IS NULL;
UPDATE menu_items SET language = 'tr' WHERE language IS NULL;
UPDATE content SET language = 'tr' WHERE language IS NULL;

-- Copy existing Turkish content to English fields as starting point
-- This gives you a base to work with - you can edit these through the admin panel

UPDATE blog_posts SET 
    title_en = title,
    excerpt_en = excerpt,
    content_en = content
WHERE title_en IS NULL OR title_en = '';

UPDATE menu_items SET 
    name_en = name,
    description_en = description
WHERE name_en IS NULL OR name_en = '';

UPDATE content SET 
    title_en = title,
    content_en = content
WHERE title_en IS NULL OR title_en = '';

-- Add comments for documentation
COMMENT ON COLUMN blog_posts.language IS 'Primary language of the content (tr/en)';
COMMENT ON COLUMN blog_posts.title_en IS 'English translation of title';
COMMENT ON COLUMN blog_posts.excerpt_en IS 'English translation of excerpt';
COMMENT ON COLUMN blog_posts.content_en IS 'English translation of content';

COMMENT ON COLUMN menu_items.language IS 'Primary language of the content (tr/en)';
COMMENT ON COLUMN menu_items.name_en IS 'English translation of name';
COMMENT ON COLUMN menu_items.description_en IS 'English translation of description';

COMMENT ON COLUMN content.language IS 'Primary language of the content (tr/en)';
COMMENT ON COLUMN content.title_en IS 'English translation of title';
COMMENT ON COLUMN content.content_en IS 'English translation of content';

-- Enable Row Level Security (RLS) for better security (if not already enabled)
ALTER TABLE content ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE menu_items ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access (if they don't exist)
DO $$ 
BEGIN
    -- Check if policies exist before creating them
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'content' AND policyname = 'Allow public read access on content') THEN
        CREATE POLICY "Allow public read access on content" ON content FOR SELECT USING (true);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'blog_posts' AND policyname = 'Allow public read access on blog_posts') THEN
        CREATE POLICY "Allow public read access on blog_posts" ON blog_posts FOR SELECT USING (published = true);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'menu_items' AND policyname = 'Allow public read access on menu_items') THEN
        CREATE POLICY "Allow public read access on menu_items" ON menu_items FOR SELECT USING (is_available = true);
    END IF;
    
    -- Create policies for authenticated users (admin) full access
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'content' AND policyname = 'Allow authenticated full access on content') THEN
        CREATE POLICY "Allow authenticated full access on content" ON content FOR ALL USING (auth.role() = 'authenticated');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'blog_posts' AND policyname = 'Allow authenticated full access on blog_posts') THEN
        CREATE POLICY "Allow authenticated full access on blog_posts" ON blog_posts FOR ALL USING (auth.role() = 'authenticated');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'menu_items' AND policyname = 'Allow authenticated full access on menu_items') THEN
        CREATE POLICY "Allow authenticated full access on menu_items" ON menu_items FOR ALL USING (auth.role() = 'authenticated');
    END IF;
END $$;

-- Show summary of what was done
SELECT 
    'blog_posts' as table_name,
    COUNT(*) as total_rows,
    COUNT(CASE WHEN title_en IS NOT NULL AND title_en != '' THEN 1 END) as rows_with_english
FROM blog_posts
UNION ALL
SELECT 
    'menu_items' as table_name,
    COUNT(*) as total_rows,
    COUNT(CASE WHEN name_en IS NOT NULL AND name_en != '' THEN 1 END) as rows_with_english
FROM menu_items
UNION ALL
SELECT 
    'content' as table_name,
    COUNT(*) as total_rows,
    COUNT(CASE WHEN title_en IS NOT NULL AND title_en != '' THEN 1 END) as rows_with_english
FROM content;
