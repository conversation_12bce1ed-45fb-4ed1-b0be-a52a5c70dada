-- Add multi-language support to existing tables
-- Run this in your Supabase SQL Editor

-- First, let's check what tables exist and create missing ones
-- Create content table if it doesn't exist (used for content sections)
CREATE TABLE IF NOT EXISTS content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    image_url TEXT,
    display_order INTEGER DEFAULT 0,
    section VARCHAR(255) NOT NULL,
    page VARCHAR(255) NOT NULL,
    language VARCHAR(2) DEFAULT 'tr',
    title_en TEXT,
    content_en TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create blog_posts table if it doesn't exist
CREATE TABLE IF NOT EXISTS blog_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT NOT NULL,
    image_url TEXT,
    author <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    category VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE,
    published BOOLEAN DEFAULT false,
    language VARCHAR(2) DEFAULT 'tr',
    title_en TEXT,
    excerpt_en TEXT,
    content_en TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create menu_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS menu_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    description TEXT NOT NULL,
    description_en TEXT,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(255) NOT NULL,
    menu_type VARCHAR(50) DEFAULT 'restaurant',
    image_url TEXT,
    is_vegetarian BOOLEAN DEFAULT false,
    is_spicy BOOLEAN DEFAULT false,
    is_available BOOLEAN DEFAULT true,
    has_sizes BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    language VARCHAR(2) DEFAULT 'tr',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create contact_submissions table if it doesn't exist
CREATE TABLE IF NOT EXISTS contact_submissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    message TEXT NOT NULL,
    recaptcha_score DECIMAL(3,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Now add language columns to existing tables (if they don't have them)
-- Add language columns to blog_posts table
ALTER TABLE blog_posts
ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr',
ADD COLUMN IF NOT EXISTS title_en TEXT,
ADD COLUMN IF NOT EXISTS excerpt_en TEXT,
ADD COLUMN IF NOT EXISTS content_en TEXT;

-- Add language columns to menu_items table
ALTER TABLE menu_items
ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr',
ADD COLUMN IF NOT EXISTS name_en TEXT,
ADD COLUMN IF NOT EXISTS description_en TEXT;

-- Add language columns to content table
ALTER TABLE content
ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr',
ADD COLUMN IF NOT EXISTS title_en TEXT,
ADD COLUMN IF NOT EXISTS content_en TEXT;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_language ON blog_posts(language);
CREATE INDEX IF NOT EXISTS idx_menu_items_language ON menu_items(language);
CREATE INDEX IF NOT EXISTS idx_content_language ON content(language);
CREATE INDEX IF NOT EXISTS idx_content_page_section ON content(page, section);
CREATE INDEX IF NOT EXISTS idx_menu_items_category ON menu_items(category);
CREATE INDEX IF NOT EXISTS idx_menu_items_type ON menu_items(menu_type);

-- Update existing data to have Turkish as default language
UPDATE blog_posts SET language = 'tr' WHERE language IS NULL;
UPDATE menu_items SET language = 'tr' WHERE language IS NULL;
UPDATE content SET language = 'tr' WHERE language IS NULL;

-- Copy Turkish content to English fields as starting point
UPDATE blog_posts SET
    title_en = title,
    excerpt_en = excerpt,
    content_en = content
WHERE title_en IS NULL OR title_en = '';

UPDATE menu_items SET
    name_en = name,
    description_en = description
WHERE name_en IS NULL OR name_en = '';

UPDATE content SET
    title_en = title,
    content_en = content
WHERE title_en IS NULL OR title_en = '';

-- Add comments for documentation
COMMENT ON COLUMN blog_posts.language IS 'Primary language of the content (tr/en)';
COMMENT ON COLUMN blog_posts.title_en IS 'English translation of title';
COMMENT ON COLUMN blog_posts.excerpt_en IS 'English translation of excerpt';
COMMENT ON COLUMN blog_posts.content_en IS 'English translation of content';

COMMENT ON COLUMN menu_items.language IS 'Primary language of the content (tr/en)';
COMMENT ON COLUMN menu_items.name_en IS 'English translation of name';
COMMENT ON COLUMN menu_items.description_en IS 'English translation of description';

COMMENT ON COLUMN content.language IS 'Primary language of the content (tr/en)';
COMMENT ON COLUMN content.title_en IS 'English translation of title';
COMMENT ON COLUMN content.content_en IS 'English translation of content';

-- Enable Row Level Security (RLS) for better security
ALTER TABLE content ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE menu_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY "Allow public read access on content" ON content FOR SELECT USING (true);
CREATE POLICY "Allow public read access on blog_posts" ON blog_posts FOR SELECT USING (published = true);
CREATE POLICY "Allow public read access on menu_items" ON menu_items FOR SELECT USING (is_available = true);

-- Create policies for authenticated users (admin) full access
CREATE POLICY "Allow authenticated full access on content" ON content FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated full access on blog_posts" ON blog_posts FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated full access on menu_items" ON menu_items FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated full access on contact_submissions" ON contact_submissions FOR ALL USING (auth.role() = 'authenticated');

-- Allow public insert on contact_submissions (for contact form)
CREATE POLICY "Allow public insert on contact_submissions" ON contact_submissions FOR INSERT WITH CHECK (true);
