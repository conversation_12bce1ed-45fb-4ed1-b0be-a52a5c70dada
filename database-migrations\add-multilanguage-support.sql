-- Add multi-language support to existing tables
-- Run this in your Supabase SQL Editor

-- Add language columns to blog_posts table
ALTER TABLE blog_posts 
ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr',
ADD COLUMN IF NOT EXISTS title_en TEXT,
ADD COLUMN IF NOT EXISTS excerpt_en TEXT,
ADD COLUMN IF NOT EXISTS content_en TEXT;

-- Add language columns to menu_items table
ALTER TABLE menu_items 
ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr',
ADD COLUMN IF NOT EXISTS name_en TEXT,
ADD COLUMN IF NOT EXISTS description_en TEXT;

-- Add language columns to content_sections table
ALTER TABLE content_sections 
ADD COLUMN IF NOT EXISTS language VARCHAR(2) DEFAULT 'tr',
ADD COLUMN IF NOT EXISTS title_en TEXT,
ADD COLUMN IF NOT EXISTS content_en TEXT;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_language ON blog_posts(language);
CREATE INDEX IF NOT EXISTS idx_menu_items_language ON menu_items(language);
CREATE INDEX IF NOT EXISTS idx_content_sections_language ON content_sections(language);

-- Update existing data to have Turkish as default language
UPDATE blog_posts SET language = 'tr' WHERE language IS NULL;
UPDATE menu_items SET language = 'tr' WHERE language IS NULL;
UPDATE content_sections SET language = 'tr' WHERE language IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN blog_posts.language IS 'Primary language of the content (tr/en)';
COMMENT ON COLUMN blog_posts.title_en IS 'English translation of title';
COMMENT ON COLUMN blog_posts.excerpt_en IS 'English translation of excerpt';
COMMENT ON COLUMN blog_posts.content_en IS 'English translation of content';

COMMENT ON COLUMN menu_items.language IS 'Primary language of the content (tr/en)';
COMMENT ON COLUMN menu_items.name_en IS 'English translation of name';
COMMENT ON COLUMN menu_items.description_en IS 'English translation of description';

COMMENT ON COLUMN content_sections.language IS 'Primary language of the content (tr/en)';
COMMENT ON COLUMN content_sections.title_en IS 'English translation of title';
COMMENT ON COLUMN content_sections.content_en IS 'English translation of content';
