# 🚀 Deployment Guide for <PERSON><PERSON>'s Burger

## Option 1: Shared Hosting (cPanel/FTP) - RECOMMENDED FOR YOU

### Step 1: Prepare Production Configuration
1. Open `src/config/production.ts`
2. Replace `your-service-role-key-here` with your actual Supabase service role key
3. Verify the Supabase URL and anon key are correct

### Step 2: Build the Project
```bash
npm run build
```

### Step 3: Upload to Shared Hosting
1. **Via cPanel File Manager:**
   - Login to your cPanel
   - Open File Manager
   - Navigate to `public_html` (or your domain's folder)
   - Delete any existing files
   - Upload ALL files from the `dist` folder
   - Make sure `.htaccess` file is uploaded

2. **Via FTP:**
   - Connect to your hosting via FTP
   - Navigate to `public_html` or `www` folder
   - Upload ALL contents of the `dist` folder
   - Ensure `.htaccess` file is uploaded

### Step 4: Configure Domain
- If using a subdomain, upload to the subdomain's folder
- If using main domain, upload to `public_html`
- Make sure your domain points to the correct folder

### Step 5: Test the Deployment
1. Visit your website URL
2. Test all pages load correctly
3. Test admin panel: `yoursite.com/admin`
4. Test menu management functionality
5. Test language switching

---

## Option 2: Vercel (Alternative - Free)

### Step 1: Prepare for Deployment
1. Make sure all your changes are saved
2. Test the build locally:
   ```bash
   npm run build
   npm run preview
   ```

### Step 2: Deploy to Vercel
1. Go to [vercel.com](https://vercel.com)
2. Sign up/login with GitHub
3. Click "New Project"
4. Import your GitHub repository
5. Configure environment variables:
   - `VITE_SUPABASE_URL`: https://vvwnzkpscijvpbiobykh.supabase.co
   - `VITE_SUPABASE_ANON_KEY`: your-anon-key
   - `VITE_SUPABASE_SERVICE_ROLE_KEY`: your-service-role-key
6. Click "Deploy"

### Step 3: Custom Domain (Optional)
1. In Vercel dashboard, go to your project
2. Go to "Settings" → "Domains"
3. Add your custom domain

---

## Option 2: Netlify (Alternative)

### Step 1: Build Settings
- Build command: `npm run build`
- Publish directory: `dist`

### Step 2: Environment Variables
Add the same environment variables as Vercel

---

## Option 3: Traditional Hosting (cPanel/FTP)

### Step 1: Build the Project
```bash
npm run build
```

### Step 2: Upload Files
1. Upload everything from the `dist` folder to your hosting's public_html
2. Configure environment variables in your hosting control panel

---

## Important Notes

### Security
- Never commit `.env` file to Git
- Use environment variables in production
- Consider using different Supabase projects for development/production

### Performance
- The build is optimized and minified
- Images are served from external CDNs
- Consider adding a CDN for better global performance

### Monitoring
- Set up error tracking (Sentry)
- Monitor performance (Vercel Analytics)
- Set up uptime monitoring

### SSL Certificate
- Vercel/Netlify provide free SSL
- For custom hosting, ensure SSL is enabled

### Database
- Your Supabase database is already configured
- No additional database setup needed
- Consider setting up proper RLS policies for production

## Post-Deployment Checklist

- [ ] Test all pages load correctly
- [ ] Test menu management in admin panel
- [ ] Test language switching
- [ ] Test contact forms
- [ ] Test mobile responsiveness
- [ ] Set up proper authentication for admin panel
- [ ] Configure proper RLS policies
- [ ] Set up backup strategy
- [ ] Configure monitoring and alerts
