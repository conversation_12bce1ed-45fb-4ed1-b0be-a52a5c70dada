-- Menu Import Script for <PERSON><PERSON>'s Burger
-- This script will insert all menu items with Turkish names and categories

-- First, let's clear existing menu items (optional - remove this line if you want to keep existing items)
-- DELETE FROM menu_items;

-- CAFE MENU ITEMS

-- <PERSON><PERSON><PERSON><PERSON> (Hot Coffees)
INSERT INTO menu_items (name, name_en, description, description_en, price, category, menu_type, is_available, display_order) VALUES
('Türk Kahvesi', 'Turkish Coffee', 'Geleneksel Türk kahvesi', 'Traditional Turkish coffee', 120, 'Sıcak Kahveler', 'cafe', true, 1),
('Filtre Kahve', 'Filter Coffee', 'Taze çekilmiş filtre kahve', 'Freshly ground filter coffee', 150, '<PERSON><PERSON>ca<PERSON>hveler', 'cafe', true, 2),
('Espresso', 'Espresso', 'İtalyan usulü espresso', 'Italian style espresso', NULL, '<PERSON>ıcak <PERSON>veler', 'cafe', true, 3),
('Americano', 'Americano', 'Espresso ve sıcak su karışımı', 'E<PERSON>resso with hot water', 150, '<PERSON>ıcak Kahveler', 'cafe', true, 4),
('Cortado', 'Cortado', 'Espresso ve az miktarda süt', 'Espresso with small amount of milk', 155, 'Sıcak Kahveler', 'cafe', true, 5),
('Flat White', 'Flat White', 'Güçlü espresso ve mikroköpük süt', 'Strong espresso with microfoam milk', 155, 'Sıcak Kahveler', 'cafe', true, 6),
('Cappuccino', 'Cappuccino', 'Espresso, süt ve süt köpüğü', 'Espresso, milk and milk foam', 155, 'Sıcak Kahveler', 'cafe', true, 7),
('Cafe Latte', 'Cafe Latte', 'Espresso ve bol miktarda süt', 'Espresso with plenty of milk', 160, 'Sıcak Kahveler', 'cafe', true, 8),
('Mocha', 'Mocha', 'Espresso, çikolata ve süt', 'Espresso, chocolate and milk', 170, 'Sıcak Kahveler', 'cafe', true, 9),
('White Chocolate Mocha', 'White Chocolate Mocha', 'Beyaz çikolatalı mocha', 'White chocolate mocha', 170, 'Sıcak Kahveler', 'cafe', true, 10);

-- Demleme Kahveler (Brew Coffees)
INSERT INTO menu_items (name, name_en, description, description_en, price, category, menu_type, is_available, display_order) VALUES
('Nitelikli Kahveler', 'Quality Coffees', 'Özel seçilmiş nitelikli kahve çekirdekleri', 'Specially selected quality coffee beans', 170, 'Demleme Kahveler', 'cafe', true, 11),
('Mikrolot Kahveler', 'Microlot Coffees', 'Sınırlı üretim mikrolot kahveler', 'Limited production microlot coffees', 190, 'Demleme Kahveler', 'cafe', true, 12),
('Yarışma Serisi Kahveler', 'Competition Series Coffees', 'Dünya çapında ödüllü kahveler', 'World award-winning coffees', 340, 'Demleme Kahveler', 'cafe', true, 13);

-- Soğuk Kahveler (Cold Coffees)
INSERT INTO menu_items (name, name_en, description, description_en, price, category, menu_type, is_available, display_order) VALUES
('Cold Brew No:1', 'Cold Brew No:1', 'Soğuk demleme kahve özel karışım', 'Cold brew coffee special blend', 180, 'Soğuk Kahveler', 'cafe', true, 14),
('Cold Brew No:2', 'Cold Brew No:2', 'Premium soğuk demleme kahve', 'Premium cold brew coffee', 190, 'Soğuk Kahveler', 'cafe', true, 15),
('Iced Americano', 'Iced Americano', 'Buzlu americano', 'Iced americano', 160, 'Soğuk Kahveler', 'cafe', true, 16),
('Iced Latte', 'Iced Latte', 'Buzlu latte', 'Iced latte', 170, 'Soğuk Kahveler', 'cafe', true, 17),
('Iced Flat White', 'Iced Flat White', 'Buzlu flat white', 'Iced flat white', 165, 'Soğuk Kahveler', 'cafe', true, 18),
('Iced Mocha', 'Iced Mocha', 'Buzlu mocha', 'Iced mocha', 180, 'Soğuk Kahveler', 'cafe', true, 19);

-- Çaylar & Diğer İçecekler (Teas & Other Drinks)
INSERT INTO menu_items (name, name_en, description, description_en, price, category, menu_type, is_available, display_order) VALUES
('Demleme Çay', 'Brewed Tea', 'Geleneksel demleme çay', 'Traditional brewed tea', 80, 'Çaylar & Diğer İçecekler', 'cafe', true, 20),
('Earl Grey Çay', 'Earl Grey Tea', 'Bergamot aromalı Earl Grey', 'Bergamot flavored Earl Grey', 140, 'Çaylar & Diğer İçecekler', 'cafe', true, 21),
('Yeşil Çay', 'Green Tea', 'Antioksidan açısından zengin yeşil çay', 'Antioxidant rich green tea', 140, 'Çaylar & Diğer İçecekler', 'cafe', true, 22),
('Özel Harman Kış Çayı', 'Special Winter Tea Blend', 'Kış mevsimi özel harman çay', 'Winter season special blend tea', 140, 'Çaylar & Diğer İçecekler', 'cafe', true, 23),
('Limonata', 'Lemonade', 'Taze sıkılmış limonata', 'Fresh squeezed lemonade', 150, 'Çaylar & Diğer İçecekler', 'cafe', true, 24),
('Su', 'Water', 'İçme suyu', 'Drinking water', 45, 'Çaylar & Diğer İçecekler', 'cafe', true, 25),
('Maden Suyu', 'Mineral Water', 'Doğal maden suyu', 'Natural mineral water', 70, 'Çaylar & Diğer İçecekler', 'cafe', true, 26);

-- Tuzlu / Tatlı Atıştırmalıklar (Salty / Sweet Snacks)
INSERT INTO menu_items (name, name_en, description, description_en, price, category, menu_type, is_available, display_order) VALUES
('Tuzlu Kiş', 'Savory Quiche', 'Ev yapımı tuzlu kiş', 'Homemade savory quiche', 210, 'Tuzlu / Tatlı Atıştırmalıklar', 'cafe', true, 27),
('Brownie', 'Brownie', 'Çikolatalı brownie', 'Chocolate brownie', 210, 'Tuzlu / Tatlı Atıştırmalıklar', 'cafe', true, 28),
('Cookie', 'Cookie', 'Ev yapımı kurabiye', 'Homemade cookie', 150, 'Tuzlu / Tatlı Atıştırmalıklar', 'cafe', true, 29),
('Cheesecake', 'Cheesecake', 'Kremalı cheesecake', 'Creamy cheesecake', 200, 'Tuzlu / Tatlı Atıştırmalıklar', 'cafe', true, 30),
('Supangle', 'Supangle', 'Çikolatalı supangle tatlısı', 'Chocolate supangle dessert', 150, 'Tuzlu / Tatlı Atıştırmalıklar', 'cafe', true, 31);

-- BURGER & HOTDOG MENU ITEMS

-- Hamburgerler (Hamburgers)
INSERT INTO menu_items (name, name_en, description, description_en, price, category, menu_type, is_available, display_order) VALUES
('Classic Burger', 'Classic Burger', 'Klasik hamburger', 'Classic hamburger', 350, 'Hamburgerler', 'restaurant', true, 50),
('Cheese Burger', 'Cheese Burger', 'Peynirli hamburger', 'Cheese hamburger', 360, 'Hamburgerler', 'restaurant', true, 51),
('Trüflüm Burger', 'Truffle Burger', 'Trüf soslu özel burger', 'Special burger with truffle sauce', 365, 'Hamburgerler', 'restaurant', true, 52),
('Barbekü Burger', 'BBQ Burger', 'Barbekü soslu burger', 'BBQ sauce burger', 365, 'Hamburgerler', 'restaurant', true, 53),
('Sebzelim Burger', 'Veggie Burger', 'Sebzeli burger', 'Vegetable burger', 325, 'Hamburgerler', 'restaurant', true, 54),
('Hottaste Burger', 'Hottaste Burger', 'Acılı özel burger', 'Spicy special burger', 365, 'Hamburgerler', 'restaurant', true, 55),
('Sam Amca Burger', 'Uncle Sam Burger', 'Amerikan usulü burger', 'American style burger', 390, 'Hamburgerler', 'restaurant', true, 56),
('Cuba Burger', 'Cuba Burger', 'Küba usulü burger', 'Cuban style burger', 390, 'Hamburgerler', 'restaurant', true, 57),
('Texas Burger', 'Texas Burger', 'Teksas usulü burger', 'Texas style burger', 390, 'Hamburgerler', 'restaurant', true, 58),
('Cheddar Bomb Burger', 'Cheddar Bomb Burger', 'Cheddar peyniri bombası', 'Cheddar cheese bomb', 480, 'Hamburgerler', 'restaurant', true, 59),
('Boss Burger', 'Boss Burger', 'Patron burgeri - en büyük', 'Boss burger - the biggest', 445, 'Hamburgerler', 'restaurant', true, 60),
('Ananas Burger', 'Pineapple Burger', 'Ananaslı burger', 'Pineapple burger', 365, 'Hamburgerler', 'restaurant', true, 61),
('French Burger', 'French Burger', 'Fransız usulü burger', 'French style burger', 395, 'Hamburgerler', 'restaurant', true, 62),
('Çocuk Menü', 'Kids Menu', 'Çocuklar için özel menü', 'Special menu for kids', 350, 'Hamburgerler', 'restaurant', true, 63);

-- Chicken Burgerler (Chicken Burgers)
INSERT INTO menu_items (name, name_en, description, description_en, price, category, menu_type, is_available, display_order) VALUES
('Chicken Burger', 'Chicken Burger', 'Tavuklu burger', 'Chicken burger', 310, 'Chicken Burgerler', 'restaurant', true, 70),
('Barbekü Chicken Burger', 'BBQ Chicken Burger', 'Barbekü soslu tavuk burger', 'BBQ chicken burger', 320, 'Chicken Burgerler', 'restaurant', true, 71),
('Atom Chicken Burger', 'Atom Chicken Burger', 'Acılı tavuk burger', 'Spicy chicken burger', 320, 'Chicken Burgerler', 'restaurant', true, 72),
('Mushroom Chicken Burger', 'Mushroom Chicken Burger', 'Mantarlı tavuk burger', 'Mushroom chicken burger', 320, 'Chicken Burgerler', 'restaurant', true, 73),
('Trüflüm Chicken Burger', 'Truffle Chicken Burger', 'Trüf soslu tavuk burger', 'Truffle chicken burger', 320, 'Chicken Burgerler', 'restaurant', true, 74);

-- Hot Doglar (Hot Dogs)
INSERT INTO menu_items (name, name_en, description, description_en, price, category, menu_type, is_available, display_order) VALUES
('Classic Hot Dog', 'Classic Hot Dog', 'Klasik hot dog', 'Classic hot dog', 310, 'Hot Doglar', 'restaurant', true, 80),
('Barbekü Hot Dog', 'BBQ Hot Dog', 'Barbekü soslu hot dog', 'BBQ hot dog', 315, 'Hot Doglar', 'restaurant', true, 81),
('Trüflü Hot Dog', 'Truffle Hot Dog', 'Trüf soslu hot dog', 'Truffle hot dog', 315, 'Hot Doglar', 'restaurant', true, 82),
('Double Hot Dog', 'Double Hot Dog', 'Çifte sosisli hot dog', 'Double sausage hot dog', 395, 'Hot Doglar', 'restaurant', true, 83),
('Kaburga Füme Hot Dog', 'Smoked Ribs Hot Dog', 'Füme kaburga etli hot dog', 'Smoked ribs hot dog', 395, 'Hot Doglar', 'restaurant', true, 84),
('Bacon Hot Dog', 'Bacon Hot Dog', 'Domuz pastırmalı hot dog', 'Bacon hot dog', 395, 'Hot Doglar', 'restaurant', true, 85);

-- UPDATE ITEMS WITH SPECIAL PRICING

-- Update Espresso with size-based pricing
UPDATE menu_items SET
    has_sizes = true,
    price_small = 120,
    price_large = 130,
    price = NULL
WHERE name = 'Espresso';

-- Update Competition Series with average price (can be customized in admin)
UPDATE menu_items SET
    price = 340,
    description = 'Dünya çapında ödüllü kahveler (230-450₺ arası)',
    description_en = 'World award-winning coffees (230-450₺ range)'
WHERE name = 'Yarışma Serisi Kahveler';

-- Add Vegan Milk option as separate item
INSERT INTO menu_items (name, name_en, description, description_en, price, category, menu_type, is_available, display_order) VALUES
('Vegan Süt Farkı', 'Vegan Milk Difference', 'Vegan süt seçeneği için ek ücret', 'Additional charge for vegan milk option', 30, 'Çaylar & Diğer İçecekler', 'cafe', true, 32);
