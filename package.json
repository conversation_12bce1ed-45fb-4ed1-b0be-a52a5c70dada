{"name": "shebos-burger", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && npm run generate:sitemap", "build:static": "vite build", "lint": "eslint .", "preview": "vite preview", "generate:sitemap": "node scripts/generate-sitemap.js", "validate:sitemap": "node scripts/validate-sitemap.js", "migrate:blog-slugs": "node scripts/migrate-blog-slugs.js", "add:slug-column": "node scripts/add-slug-column.js", "sitemap:watch": "node scripts/webhook-sitemap-update.js"}, "dependencies": {"@supabase/supabase-js": "^2.39.7", "@tiptap/extension-color": "^2.1.13", "@tiptap/extension-highlight": "^2.1.13", "@tiptap/extension-image": "^2.1.13", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-text-align": "^2.1.13", "@tiptap/pm": "^2.1.13", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "date-fns": "^3.3.1", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.51.0", "react-query": "^3.39.3", "react-router-dom": "^6.22.3"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "dotenv": "^16.5.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "supabase": "^2.23.4", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}