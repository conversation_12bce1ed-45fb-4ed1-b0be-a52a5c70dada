/**
 * Dynamic Sitemap API Endpoint
 * This can be used with Netlify Functions, Vercel API Routes, or similar
 * 
 * For shared hosting, use the PHP version instead
 */

// Supabase configuration
const SUPABASE_URL = 'https://vvwnzkpscijvpbiobykh.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ2d256a3BzY2lqdnBiaW9ieWtoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNzY4NjIsImV4cCI6MjA2Mzk1Mjg2Mn0.KSwbJzhZdifpEL1kT0QTcZq7H97czUasFk3NJnqNTmU';
const SITE_URL = 'https://www.shebosburger.com';

const staticRoutes = [
  { url: '/', changefreq: 'daily', priority: '1.0' },
  { url: '/menu', changefreq: 'daily', priority: '1.0' },
  { url: '/blog', changefreq: 'daily', priority: '1.0' },
  { url: '/privacy-policy', changefreq: 'daily', priority: '1.0' },
  { url: '/terms-of-service', changefreq: 'daily', priority: '1.0' }
];

async function fetchBlogPosts() {
  try {
    const response = await fetch(
      `${SUPABASE_URL}/rest/v1/blog_posts?published=eq.true&select=id,slug,created_at&order=created_at.desc`,
      {
        headers: {
          'apikey': SUPABASE_KEY,
          'Authorization': `Bearer ${SUPABASE_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return [];
  }
}

export default async function handler(req, res) {
  try {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Content-Type', 'application/xml; charset=utf-8');

    // Fetch blog posts
    const blogPosts = await fetchBlogPosts();

    // Generate XML
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Add static routes
    staticRoutes.forEach(route => {
      const lastmod = new Date().toISOString().split('T')[0];
      xml += `
  <url>
    <loc>${SITE_URL}${route.url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${route.changefreq}</changefreq>
    <priority>${route.priority}</priority>
  </url>`;
    });

    // Add blog posts
    blogPosts.forEach(post => {
      const lastmod = new Date(post.created_at).toISOString().split('T')[0];
      const urlPath = post.slug || post.id;
      xml += `
  <url>
    <loc>${SITE_URL}/blog/${urlPath}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>`;
    });

    xml += `
</urlset>`;

    res.status(200).send(xml);
  } catch (error) {
    console.error('Error generating sitemap:', error);
    res.status(500).json({ error: 'Failed to generate sitemap' });
  }
}

// For Node.js environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = handler;
}
