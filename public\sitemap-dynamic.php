<?php
/**
 * Dynamic Sitemap Generator for <PERSON><PERSON>'s Burger
 * This PHP script generates sitemap.xml dynamically from Supabase
 * Place this file as sitemap.xml on your server (rename to sitemap.xml)
 */

// Set content type to XML
header('Content-Type: application/xml; charset=utf-8');

// Supabase configuration
$supabaseUrl = 'https://vvwnzkpscijvpbiobykh.supabase.co';
$supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ2d256a3BzY2lqdnBiaW9ieWtoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNzY4NjIsImV4cCI6MjA2Mzk1Mjg2Mn0.KSwbJzhZdifpEL1kT0QTcZq7H97czUasFk3NJnqNTmU';
$siteUrl = 'https://www.shebosburger.com';

// Static routes
$staticRoutes = [
    ['url' => '/', 'changefreq' => 'daily', 'priority' => '1.0'],
    ['url' => '/menu', 'changefreq' => 'daily', 'priority' => '1.0'],
    ['url' => '/blog', 'changefreq' => 'daily', 'priority' => '1.0'],
    ['url' => '/privacy-policy', 'changefreq' => 'daily', 'priority' => '1.0'],
    ['url' => '/terms-of-service', 'changefreq' => 'daily', 'priority' => '1.0']
];

// Function to fetch blog posts from Supabase
function fetchBlogPosts($supabaseUrl, $supabaseKey) {
    $url = $supabaseUrl . '/rest/v1/blog_posts?published=eq.true&select=id,slug,created_at&order=created_at.desc';
    
    $headers = [
        'apikey: ' . $supabaseKey,
        'Authorization: Bearer ' . $supabaseKey,
        'Content-Type: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    }
    
    return [];
}

// Start XML output
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

// Add static routes
foreach ($staticRoutes as $route) {
    $lastmod = date('Y-m-d');
    echo "  <url>\n";
    echo "    <loc>{$siteUrl}{$route['url']}</loc>\n";
    echo "    <lastmod>{$lastmod}</lastmod>\n";
    echo "    <changefreq>{$route['changefreq']}</changefreq>\n";
    echo "    <priority>{$route['priority']}</priority>\n";
    echo "  </url>\n";
}

// Fetch and add blog posts
$blogPosts = fetchBlogPosts($supabaseUrl, $supabaseKey);

foreach ($blogPosts as $post) {
    $urlPath = !empty($post['slug']) ? $post['slug'] : $post['id'];
    $lastmod = date('Y-m-d', strtotime($post['created_at']));
    
    echo "  <url>\n";
    echo "    <loc>{$siteUrl}/blog/{$urlPath}</loc>\n";
    echo "    <lastmod>{$lastmod}</lastmod>\n";
    echo "    <changefreq>daily</changefreq>\n";
    echo "    <priority>1.0</priority>\n";
    echo "  </url>\n";
}

// Close XML
echo '</urlset>';
?>
