import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Only create Supabase client if credentials are provided
let supabase = null;
if (supabaseUrl && supabaseKey && !supabaseUrl.includes('your-supabase-url')) {
  supabase = createClient(supabaseUrl, supabaseKey);
}

// Website configuration
const SITE_URL = process.env.SITE_URL || 'https://www.shebosburger.com';
const OUTPUT_PATH = path.join(process.cwd(), 'public', 'sitemap.xml');

// Static routes configuration - All priority 1.0 and daily updates
const staticRoutes = [
  {
    url: '/',
    changefreq: 'daily',
    priority: '1.0'
  },
  {
    url: '/menu',
    changefreq: 'daily',
    priority: '1.0'
  },
  {
    url: '/blog',
    changefreq: 'daily',
    priority: '1.0'
  },
  {
    url: '/privacy-policy',
    changefreq: 'daily',
    priority: '1.0'
  },
  {
    url: '/terms-of-service',
    changefreq: 'daily',
    priority: '1.0'
  }
];

async function generateSitemap() {
  try {
    console.log('🚀 Generating sitemap...');

    // Fetch published blog posts
    let blogPosts = null;
    let error = null;

    if (supabase) {
      try {
        const result = await supabase
          .from('blog_posts')
          .select('id, slug, created_at')
          .eq('published', true)
          .order('created_at', { ascending: false });

        blogPosts = result.data;
        error = result.error;
      } catch (err) {
        error = err;
      }
    } else {
      console.log('⚠️  Supabase not configured - generating static sitemap only');
    }

    if (error) {
      console.warn('⚠️  Could not fetch blog posts:', error.message);
      console.log('📝 Generating sitemap with static routes only...');
    }

    // Generate clean XML without any script tags or extra content
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';

    // Add static routes
    staticRoutes.forEach(route => {
      const lastmod = new Date().toISOString().split('T')[0];
      xml += `
  <url>
    <loc>${SITE_URL}${route.url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${route.changefreq}</changefreq>
    <priority>${route.priority}</priority>
  </url>`;
    });

    // Add blog posts if available
    if (blogPosts && blogPosts.length > 0) {
      console.log(`📰 Adding ${blogPosts.length} blog posts to sitemap...`);

      blogPosts.forEach(post => {
        const lastmod = new Date(post.created_at).toISOString().split('T')[0];
        // Use slug if available, fallback to ID for backward compatibility
        const urlPath = post.slug || post.id;
        xml += `
  <url>
    <loc>${SITE_URL}/blog/${urlPath}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>`;
      });
    }

    xml += `
</urlset>`;

    // Write sitemap to file
    fs.writeFileSync(OUTPUT_PATH, xml);

    console.log('✅ Sitemap generated successfully!');
    console.log(`📍 Location: ${OUTPUT_PATH}`);
    console.log(`🔗 URL: ${SITE_URL}/sitemap.xml`);

  } catch (error) {
    console.error('❌ Error generating sitemap:', error);
    process.exit(1);
  }
}

// Run the generator
generateSitemap();
