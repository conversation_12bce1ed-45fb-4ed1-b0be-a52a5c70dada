import fs from 'fs';
import path from 'path';

const SITEMAP_PATH = path.join(process.cwd(), 'public', 'sitemap.xml');
const ROBOTS_PATH = path.join(process.cwd(), 'public', 'robots.txt');

function validateSitemap() {
  console.log('🔍 Validating SEO files...\n');

  // Check if files exist
  const sitemapExists = fs.existsSync(SITEMAP_PATH);
  const robotsExists = fs.existsSync(ROBOTS_PATH);

  console.log(`📄 robots.txt: ${robotsExists ? '✅ Found' : '❌ Missing'}`);
  console.log(`🗺️  sitemap.xml: ${sitemapExists ? '✅ Found' : '❌ Missing'}\n`);

  if (!sitemapExists || !robotsExists) {
    console.log('❌ Some files are missing. Run: npm run generate:sitemap');
    process.exit(1);
  }

  // Validate robots.txt
  const robotsContent = fs.readFileSync(ROBOTS_PATH, 'utf8');
  console.log('🤖 robots.txt content:');
  console.log('─'.repeat(40));
  console.log(robotsContent);
  console.log('─'.repeat(40));

  // Basic robots.txt validation
  const hasUserAgent = robotsContent.includes('User-agent:');
  const hasSitemap = robotsContent.includes('Sitemap:');
  const hasDisallowAdmin = robotsContent.includes('Disallow: /admin/');

  console.log('\n🔍 robots.txt validation:');
  console.log(`   User-agent directive: ${hasUserAgent ? '✅' : '❌'}`);
  console.log(`   Sitemap reference: ${hasSitemap ? '✅' : '❌'}`);
  console.log(`   Admin protection: ${hasDisallowAdmin ? '✅' : '❌'}`);

  // Validate sitemap.xml
  const sitemapContent = fs.readFileSync(SITEMAP_PATH, 'utf8');
  
  // Count URLs in sitemap
  const urlMatches = sitemapContent.match(/<url>/g);
  const urlCount = urlMatches ? urlMatches.length : 0;

  console.log(`\n🗺️  sitemap.xml validation:`);
  console.log(`   Total URLs: ${urlCount}`);
  console.log(`   File size: ${(sitemapContent.length / 1024).toFixed(2)} KB`);

  // Check for required elements
  const hasXmlDeclaration = sitemapContent.includes('<?xml version="1.0"');
  const hasUrlset = sitemapContent.includes('<urlset');
  const hasHomepage = sitemapContent.includes('<loc>https://');

  console.log(`   XML declaration: ${hasXmlDeclaration ? '✅' : '❌'}`);
  console.log(`   Urlset element: ${hasUrlset ? '✅' : '❌'}`);
  console.log(`   Homepage URL: ${hasHomepage ? '✅' : '❌'}`);

  // Extract and display URLs
  const urlRegex = /<loc>(.*?)<\/loc>/g;
  const urls = [];
  let match;
  while ((match = urlRegex.exec(sitemapContent)) !== null) {
    urls.push(match[1]);
  }

  console.log('\n📋 URLs in sitemap:');
  urls.forEach((url, index) => {
    console.log(`   ${index + 1}. ${url}`);
  });

  // Check for placeholder domains
  const hasPlaceholder = sitemapContent.includes('yourdomain.com');
  if (hasPlaceholder) {
    console.log('\n⚠️  Warning: Found placeholder domain "yourdomain.com"');
    console.log('   Please update the domain in your sitemap generator script');
  }

  console.log('\n✅ Validation complete!');
  
  if (hasPlaceholder) {
    console.log('\n🔧 Next steps:');
    console.log('   1. Update SITE_URL in scripts/generate-sitemap.js');
    console.log('   2. Update domain in public/robots.txt');
    console.log('   3. Run: npm run generate:sitemap');
  }
}

// Run validation
validateSitemap();
