import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Website configuration
const SITE_URL = process.env.SITE_URL || 'https://www.shebosburger.com';
const OUTPUT_PATH = path.join(process.cwd(), 'public', 'sitemap.xml');

// Static routes configuration - All priority 1.0 and daily updates
const staticRoutes = [
  { url: '/', changefreq: 'daily', priority: '1.0' },
  { url: '/menu', changefreq: 'daily', priority: '1.0' },
  { url: '/blog', changefreq: 'daily', priority: '1.0' },
  { url: '/privacy-policy', changefreq: 'daily', priority: '1.0' },
  { url: '/terms-of-service', changefreq: 'daily', priority: '1.0' }
];

async function generateSitemap() {
  try {
    console.log('🚀 Generating sitemap automatically...');

    // Fetch published blog posts
    let blogPosts = null;
    let error = null;

    try {
      const result = await supabase
        .from('blog_posts')
        .select('id, slug, created_at')
        .eq('published', true)
        .order('created_at', { ascending: false });

      blogPosts = result.data;
      error = result.error;
    } catch (err) {
      error = err;
    }

    if (error) {
      console.warn('⚠️  Could not fetch blog posts:', error.message);
      console.log('📝 Generating sitemap with static routes only...');
    }

    // Generate clean XML without any script tags or extra content
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';

    // Add static routes
    staticRoutes.forEach(route => {
      const lastmod = new Date().toISOString().split('T')[0];
      xml += `
  <url>
    <loc>${SITE_URL}${route.url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${route.changefreq}</changefreq>
    <priority>${route.priority}</priority>
  </url>`;
    });

    // Add blog posts if available
    if (blogPosts && blogPosts.length > 0) {
      console.log(`📰 Adding ${blogPosts.length} blog posts to sitemap...`);

      blogPosts.forEach(post => {
        const lastmod = new Date(post.created_at).toISOString().split('T')[0];
        // Use slug if available, fallback to ID for backward compatibility
        const urlPath = post.slug || post.id;
        xml += `
  <url>
    <loc>${SITE_URL}/blog/${urlPath}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>`;
      });
    }

    xml += '\n</urlset>';

    // Write sitemap to file
    fs.writeFileSync(OUTPUT_PATH, xml);

    console.log('✅ Sitemap updated automatically!');
    console.log(`📍 Location: ${OUTPUT_PATH}`);
    console.log(`🔗 URL: ${SITE_URL}/sitemap.xml`);
    console.log(`📊 Total URLs: ${staticRoutes.length + (blogPosts ? blogPosts.length : 0)}`);

    return xml;

  } catch (error) {
    console.error('❌ Error generating sitemap:', error);
    throw error;
  }
}

// Set up real-time subscription to blog_posts changes
async function setupRealtimeUpdates() {
  console.log('🔄 Setting up real-time sitemap updates...');

  const subscription = supabase
    .channel('blog_posts_changes')
    .on(
      'postgres_changes',
      {
        event: '*', // Listen to all events (INSERT, UPDATE, DELETE)
        schema: 'public',
        table: 'blog_posts'
      },
      async (payload) => {
        console.log('📝 Blog post changed, updating sitemap...', {
          event: payload.eventType,
          id: payload.new?.id || payload.old?.id
        });

        // Wait a moment to ensure database is updated
        setTimeout(async () => {
          try {
            await generateSitemap();
            console.log('🎉 Sitemap updated successfully after blog post change!');
          } catch (error) {
            console.error('❌ Failed to update sitemap after blog post change:', error);
          }
        }, 1000);
      }
    )
    .subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        console.log('✅ Real-time sitemap updates are now active!');
        console.log('💡 Sitemap will automatically update when blog posts are published/unpublished');
      } else if (status === 'CHANNEL_ERROR') {
        console.error('❌ Failed to subscribe to real-time updates');
      }
    });

  // Generate initial sitemap
  await generateSitemap();

  return subscription;
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down sitemap updater...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down sitemap updater...');
  process.exit(0);
});

// Start the real-time sitemap updater
setupRealtimeUpdates().catch(error => {
  console.error('❌ Failed to start sitemap updater:', error);
  process.exit(1);
});

export { generateSitemap, setupRealtimeUpdates };
