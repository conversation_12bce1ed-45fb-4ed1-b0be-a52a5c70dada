import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Send, CheckCircle, AlertCircle } from 'lucide-react';
import { GoogleReCaptchaProvider, useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { supabase } from '../lib/supabaseClient';
import { useLanguage } from '../contexts/LanguageContext';

type ContactFormData = {
  name: string;
  email: string;
  phone: string;
  message: string;
};

type ContactSubmission = {
  id?: string;
  name: string;
  email: string;
  phone: string;
  message: string;
  recaptcha_score?: number;
  created_at?: string;
};

const ContactFormInner = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const { executeRecaptcha } = useGoogleReCaptcha();
  const { t } = useLanguage();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<ContactFormData>();

  const onSubmit = async (data: ContactFormData) => {
    if (!executeRecaptcha) {
      setErrorMessage('reCAPTCHA not available. Please try again.');
      setSubmitStatus('error');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      // Execute reCAPTCHA
      const recaptchaToken = await executeRecaptcha('contact_form');
      
      // Verify reCAPTCHA with Google (in a real app, this should be done on the server)
      const recaptchaResponse = await fetch(
        `https://www.google.com/recaptcha/api/siteverify?secret=${import.meta.env.VITE_RECAPTCHA_SECRET_KEY}&response=${recaptchaToken}`,
        { method: 'POST' }
      );
      
      const recaptchaResult = await recaptchaResponse.json();
      
      if (!recaptchaResult.success || recaptchaResult.score < 0.5) {
        throw new Error('reCAPTCHA verification failed. Please try again.');
      }

      // Submit to Supabase
      const submissionData: ContactSubmission = {
        ...data,
        recaptcha_score: recaptchaResult.score,
        created_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('contact_submissions')
        .insert([submissionData]);

      if (error) throw error;

      setSubmitStatus('success');
      reset();
    } catch (error) {
      console.error('Contact form submission error:', error);
      setErrorMessage(error instanceof Error ? error.message : 'An error occurred. Please try again.');
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      className="bg-white rounded-lg shadow-lg p-8"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-coffee mb-2">{t('contact.form.title')}</h2>
        <p className="text-gray-600">{t('contact.form.subtitle')}</p>
      </div>

      {submitStatus === 'success' && (
        <motion.div
          className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
          <p className="text-green-800">{t('contact.form.success')}</p>
        </motion.div>
      )}

      {submitStatus === 'error' && (
        <motion.div
          className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <AlertCircle className="h-5 w-5 text-red-600 mr-3" />
          <p className="text-red-800">{errorMessage || t('contact.form.error')}</p>
        </motion.div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">{t('contact.form.name')}</label>
            <input
              {...register('name', { 
                required: t('contact.form.nameRequired'),
                minLength: { value: 2, message: t('contact.form.nameMinLength') }
              })}
              className="form-input"
              placeholder={t('contact.form.namePlaceholder')}
            />
            {errors.name && (
              <p className="text-red-600 text-sm mt-1">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">{t('contact.form.email')}</label>
            <input
              type="email"
              {...register('email', { 
                required: t('contact.form.emailRequired'),
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: t('contact.form.emailInvalid')
                }
              })}
              className="form-input"
              placeholder={t('contact.form.emailPlaceholder')}
            />
            {errors.email && (
              <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="form-label">{t('contact.form.phone')}</label>
          <input
            type="tel"
            {...register('phone', { 
              required: t('contact.form.phoneRequired'),
              pattern: {
                value: /^[\+]?[0-9\s\-\(\)]{10,}$/,
                message: t('contact.form.phoneInvalid')
              }
            })}
            className="form-input"
            placeholder={t('contact.form.phonePlaceholder')}
          />
          {errors.phone && (
            <p className="text-red-600 text-sm mt-1">{errors.phone.message}</p>
          )}
        </div>

        <div>
          <label className="form-label">{t('contact.form.message')}</label>
          <textarea
            {...register('message', { 
              required: t('contact.form.messageRequired'),
              minLength: { value: 10, message: t('contact.form.messageMinLength') }
            })}
            className="form-input h-32 resize-none"
            placeholder={t('contact.form.messagePlaceholder')}
          />
          {errors.message && (
            <p className="text-red-600 text-sm mt-1">{errors.message.message}</p>
          )}
        </div>

        <div className="text-center">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`
              btn btn-primary flex items-center justify-center mx-auto
              ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                {t('contact.form.sending')}
              </>
            ) : (
              <>
                <Send className="h-5 w-5 mr-2" />
                {t('contact.form.send')}
              </>
            )}
          </button>
        </div>

        <div className="text-center text-sm text-gray-500">
          {t('contact.form.recaptcha')}
        </div>
      </form>
    </motion.div>
  );
};

const ContactForm = () => {
  const recaptchaSiteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;

  if (!recaptchaSiteKey) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center text-red-600">
          <AlertCircle className="h-8 w-8 mx-auto mb-2" />
          <p>reCAPTCHA configuration missing. Please contact the administrator.</p>
        </div>
      </div>
    );
  }

  return (
    <GoogleReCaptchaProvider reCaptchaKey={recaptchaSiteKey}>
      <ContactFormInner />
    </GoogleReCaptchaProvider>
  );
};

export default ContactForm;
