import { useEffect } from 'react';
import { supabase } from '../lib/supabaseClient';

/**
 * Dynamic Sitemap Generator Component
 * This component generates and updates sitemap.xml automatically
 * when blog posts are published/unpublished
 */

const SITE_URL = 'https://www.shebosburger.com';

const staticRoutes = [
  { url: '/', changefreq: 'daily', priority: '1.0' },
  { url: '/menu', changefreq: 'daily', priority: '1.0' },
  { url: '/blog', changefreq: 'daily', priority: '1.0' },
  { url: '/privacy-policy', changefreq: 'daily', priority: '1.0' },
  { url: '/terms-of-service', changefreq: 'daily', priority: '1.0' }
];

export const generateSitemapXML = async (): Promise<string> => {
  try {
    // Fetch published blog posts
    const { data: blogPosts, error } = await supabase
      .from('blog_posts')
      .select('id, slug, created_at')
      .eq('published', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.warn('Could not fetch blog posts for sitemap:', error);
    }

    // Generate XML
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Add static routes
    staticRoutes.forEach(route => {
      const lastmod = new Date().toISOString().split('T')[0];
      xml += `
  <url>
    <loc>${SITE_URL}${route.url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${route.changefreq}</changefreq>
    <priority>${route.priority}</priority>
  </url>`;
    });

    // Add blog posts
    if (blogPosts && blogPosts.length > 0) {
      blogPosts.forEach(post => {
        const lastmod = new Date(post.created_at).toISOString().split('T')[0];
        const urlPath = post.slug || post.id;
        xml += `
  <url>
    <loc>${SITE_URL}/blog/${urlPath}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>`;
      });
    }

    xml += `
</urlset>`;

    return xml;
  } catch (error) {
    console.error('Error generating sitemap:', error);
    throw error;
  }
};

/**
 * Hook to automatically update sitemap when blog posts change
 */
export const useDynamicSitemap = () => {
  useEffect(() => {
    // Subscribe to blog_posts changes
    const subscription = supabase
      .channel('blog_posts_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'blog_posts'
        },
        async (payload) => {
          console.log('Blog post changed, updating sitemap...', payload);
          
          try {
            const sitemapXML = await generateSitemapXML();
            
            // In a real implementation, you would send this to your server
            // For now, we'll just log it
            console.log('Updated sitemap generated:', sitemapXML.length, 'characters');
            
            // You could implement an API endpoint to update the sitemap file
            // await fetch('/api/update-sitemap', {
            //   method: 'POST',
            //   headers: { 'Content-Type': 'application/xml' },
            //   body: sitemapXML
            // });
            
          } catch (error) {
            console.error('Failed to update sitemap:', error);
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);
};

/**
 * Component that can be used in admin panel to manually trigger sitemap update
 */
const DynamicSitemap: React.FC = () => {
  const handleGenerateSitemap = async () => {
    try {
      const sitemapXML = await generateSitemapXML();
      
      // Create and download the sitemap file
      const blob = new Blob([sitemapXML], { type: 'application/xml' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'sitemap.xml';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      alert('Sitemap generated and downloaded successfully!');
    } catch (error) {
      console.error('Error generating sitemap:', error);
      alert('Failed to generate sitemap. Please try again.');
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Dynamic Sitemap</h3>
      <p className="text-gray-600 mb-4">
        Generate an updated sitemap.xml with all current blog posts.
      </p>
      <button
        onClick={handleGenerateSitemap}
        className="btn bg-coffee text-white hover:bg-coffee/90"
      >
        Generate & Download Sitemap
      </button>
    </div>
  );
};

export default DynamicSitemap;
