import { ReactNode, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Menu as MenuIcon, FileText, LayoutDashboard, LogOut, ChevronLeft, MergeIcon as BurgerIcon, Menu } from 'lucide-react';
import { supabase } from '../../lib/supabaseClient';
import { useLanguage } from '../../contexts/LanguageContext';

interface AdminLayoutProps {
  children: ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const { t } = useLanguage();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
  };

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col md:flex-row">
      {/* Mobile Header */}
      <div className="md:hidden bg-coffee text-white py-4 px-4 flex justify-between items-center">
        <Link to="/admin" className="flex items-center">
          <BurgerIcon className="h-6 w-6 mr-2" />
          <span className="font-bold">Shebo's Admin</span>
        </Link>

        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="p-2 rounded-md hover:bg-white/10"
        >
          <Menu className="h-6 w-6" />
        </button>
      </div>

      {/* Sidebar */}
      <aside
        className={`bg-coffee text-white w-full md:w-64 md:fixed md:h-screen overflow-y-auto transition-all duration-300 ease-in-out ${
          sidebarOpen ? 'block' : 'hidden md:block'
        }`}
      >
        <div className="p-6">
          <Link to="/admin" className="flex items-center">
            <BurgerIcon className="h-8 w-8 mr-2" />
            <span className="text-xl font-bold">Shebo's Admin</span>
          </Link>
        </div>

        <nav className="mt-6">
          <ul className="space-y-2 px-4">
            <NavItem
              to="/admin"
              label={t('admin.dashboard')}
              icon={<Home />}
              active={location.pathname === '/admin'}
              onClick={() => setSidebarOpen(false)}
            />
            <NavItem
              to="/admin/menu"
              label={t('admin.menu')}
              icon={<MenuIcon />}
              active={location.pathname === '/admin/menu'}
              onClick={() => setSidebarOpen(false)}
            />
            <NavItem
              to="/admin/blog"
              label={t('admin.blog')}
              icon={<FileText />}
              active={location.pathname === '/admin/blog'}
              onClick={() => setSidebarOpen(false)}
            />
            <NavItem
              to="/admin/content"
              label={t('admin.content')}
              icon={<LayoutDashboard />}
              active={location.pathname === '/admin/content'}
              onClick={() => setSidebarOpen(false)}
            />
          </ul>
        </nav>

        <div className="px-4 mt-12">
          <Link
            to="/"
            className="flex items-center py-3 px-4 rounded-md hover:bg-white/10 text-white/80 hover:text-white"
          >
            <ChevronLeft className="h-5 w-5 mr-2" />
            {t('admin.viewWebsite')}
          </Link>

          <button
            onClick={handleSignOut}
            className="flex items-center w-full py-3 px-4 rounded-md hover:bg-white/10 text-white/80 hover:text-white mt-4"
          >
            <LogOut className="h-5 w-5 mr-2" />
            {t('admin.signOut')}
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 md:ml-64 p-6">
        <div className="max-w-7xl mx-auto">
          {children}
        </div>
      </main>
    </div>
  );
};

// Nav Item Component
interface NavItemProps {
  to: string;
  label: string;
  icon: React.ReactNode;
  active: boolean;
  onClick: () => void;
}

const NavItem = ({ to, label, icon, active, onClick }: NavItemProps) => (
  <li>
    <Link
      to={to}
      className={`flex items-center py-3 px-4 rounded-md transition-colors ${
        active
          ? 'bg-white/20 text-white'
          : 'text-white/80 hover:bg-white/10 hover:text-white'
      }`}
      onClick={onClick}
    >
      <span className="h-5 w-5 mr-3">{icon}</span>
      {label}
    </Link>
  </li>
);

export default AdminLayout;