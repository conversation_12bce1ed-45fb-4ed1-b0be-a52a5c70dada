import { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabaseClient';
import { supabaseAdmin } from '../../lib/supabaseAdmin';

const DatabaseTest = () => {
  const [status, setStatus] = useState<string>('Testing...');
  const [menuCount, setMenuCount] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  const [menuItems, setMenuItems] = useState<any[]>([]);
  const [tables, setTables] = useState<any>({});

  useEffect(() => {
    testDatabase();
  }, []);

  const testDatabase = async () => {
    try {
      setStatus('Testing database connection...');

      // Test with admin client first
      console.log('Testing with admin client...');
      const { data: adminMenuData, error: adminMenuError, count: adminCount } = await supabaseAdmin
        .from('menu_items')
        .select('*', { count: 'exact' });

      if (adminMenuError) {
        console.error('Admin client error:', adminMenuError);
        setTables(prev => ({ ...prev, admin_test: { error: `Admin: ${adminMenuError.message}` } }));
      } else {
        console.log('Admin client success:', adminMenuData);
        setTables(prev => ({ ...prev, admin_test: { count: adminCount || 0, success: true } }));
      }

      // Test menu_items table with regular client
      console.log('Testing menu_items table with regular client...');
      const { data: menuData, error: menuError, count } = await supabase
        .from('menu_items')
        .select('*', { count: 'exact' });

      if (menuError) {
        setError(`Menu items error: ${menuError.message} (Code: ${menuError.code})`);
        setStatus('Menu items table failed');
        console.error('Menu error:', menuError);
      } else {
        setMenuCount(count || 0);
        setMenuItems(menuData || []);
        setTables(prev => ({ ...prev, menu_items: { count: count || 0, data: menuData } }));
        console.log('Menu items found:', menuData);
      }

      // Test blog_posts table
      const { data: blogData, error: blogError, count: blogCount } = await supabase
        .from('blog_posts')
        .select('*', { count: 'exact' });

      if (blogError) {
        console.error('Blog error:', blogError);
        setTables(prev => ({ ...prev, blog_posts: { error: blogError.message } }));
      } else {
        setTables(prev => ({ ...prev, blog_posts: { count: blogCount || 0, data: blogData } }));
        console.log('Blog posts found:', blogData);
      }

      // Test content table
      const { data: contentData, error: contentError, count: contentCount } = await supabase
        .from('content')
        .select('*', { count: 'exact' });

      if (contentError) {
        console.error('Content error:', contentError);
        setTables(prev => ({ ...prev, content: { error: contentError.message } }));
      } else {
        setTables(prev => ({ ...prev, content: { count: contentCount || 0, data: contentData } }));
        console.log('Content sections found:', contentData);
      }

      if (!menuError || !adminMenuError) {
        setStatus('Connection successful!');
        setError(null);
      }

    } catch (err) {
      setError(`Connection error: ${err}`);
      setStatus('Connection failed');
      console.error('Database test error:', err);
    }
  };

  const testInsert = async () => {
    try {
      setStatus('Testing insert...');

      const testItem = {
        name: 'Test Item',
        description: 'Test Description',
        category: 'Test',
        menu_type: 'restaurant',
        price: 10.99,
        is_available: true
      };

      const { data, error } = await supabaseAdmin
        .from('menu_items')
        .insert([testItem])
        .select();

      if (error) {
        setError(`Insert error: ${error.message}`);
        setStatus('Insert failed');
        return;
      }

      setStatus('Insert successful!');
      setError(null);

      // Clean up - delete the test item
      if (data && data[0]) {
        await supabaseAdmin
          .from('menu_items')
          .delete()
          .eq('id', data[0].id);
      }

      // Refresh the test
      testDatabase();

    } catch (err) {
      setError(`Insert error: ${err}`);
      setStatus('Insert failed');
    }
  };

  const createTables = async () => {
    try {
      setStatus('Creating tables...');

      // Create tables using admin client
      const createSQL = `
        -- Create menu_items table
        CREATE TABLE IF NOT EXISTS menu_items (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name TEXT NOT NULL,
          name_en TEXT,
          description TEXT,
          description_en TEXT,
          price DECIMAL(10,2),
          category TEXT NOT NULL,
          menu_type TEXT NOT NULL CHECK (menu_type IN ('cafe', 'restaurant')),
          image_url TEXT,
          is_vegetarian BOOLEAN DEFAULT false,
          is_spicy BOOLEAN DEFAULT false,
          is_available BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Disable RLS
        ALTER TABLE menu_items DISABLE ROW LEVEL SECURITY;

        -- Insert sample data
        INSERT INTO menu_items (name, description, category, menu_type, price, is_available) VALUES
        ('Klasik Cheeseburger', 'Sulu dana eti, eritilmiş cheddar peyniri', 'Burgerler', 'restaurant', 45.99, true),
        ('Truffle Patates', 'Truffle yağı ve parmesan peyniri', 'Yan Ürünler', 'restaurant', 28.99, true)
        ON CONFLICT DO NOTHING;
      `;

      const { error } = await supabaseAdmin.rpc('exec_sql', { sql: createSQL });

      if (error) {
        setError(`Create tables error: ${error.message}`);
        setStatus('Create tables failed');
        return;
      }

      setStatus('Tables created successfully!');
      setError(null);

      // Refresh the test
      testDatabase();

    } catch (err) {
      setError(`Create tables error: ${err}`);
      setStatus('Create tables failed');
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-bold mb-4">Database Connection Test</h3>

      <div className="space-y-4">
        <div>
          <strong>Status:</strong> <span className={status.includes('successful') ? 'text-green-600' : status.includes('failed') ? 'text-red-600' : 'text-blue-600'}>{status}</span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-3 rounded">
            <strong>Admin Client:</strong> {tables.admin_test?.success ? '✅' : '❌'}
            {tables.admin_test?.error && <div className="text-red-600 text-sm">{tables.admin_test.error}</div>}
            {tables.admin_test?.count !== undefined && <div className="text-sm">Count: {tables.admin_test.count}</div>}
          </div>
          <div className="bg-gray-50 p-3 rounded">
            <strong>Menu Items:</strong> {tables.menu_items?.count || 0}
            {tables.menu_items?.error && <div className="text-red-600 text-sm">{tables.menu_items.error}</div>}
          </div>
          <div className="bg-gray-50 p-3 rounded">
            <strong>Blog Posts:</strong> {tables.blog_posts?.count || 0}
            {tables.blog_posts?.error && <div className="text-red-600 text-sm">{tables.blog_posts.error}</div>}
          </div>
          <div className="bg-gray-50 p-3 rounded">
            <strong>Content Sections:</strong> {tables.content?.count || 0}
            {tables.content?.error && <div className="text-red-600 text-sm">{tables.content.error}</div>}
          </div>
        </div>

        {error && (
          <div className="text-red-600 bg-red-50 p-3 rounded">
            <strong>Error:</strong> {error}
          </div>
        )}

        {menuItems.length > 0 && (
          <div className="bg-green-50 p-3 rounded">
            <strong>Sample Menu Items:</strong>
            <ul className="mt-2 space-y-1">
              {menuItems.slice(0, 3).map((item, index) => (
                <li key={index} className="text-sm">
                  • {item.name} - {item.category} - ₺{item.price}
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className="flex gap-3 flex-wrap">
          <button
            onClick={testDatabase}
            className="btn bg-blue-600 text-white hover:bg-blue-700"
          >
            Refresh Test
          </button>

          <button
            onClick={testInsert}
            className="btn bg-green-600 text-white hover:bg-green-700"
          >
            Test Insert
          </button>

          <button
            onClick={createTables}
            className="btn bg-purple-600 text-white hover:bg-purple-700"
          >
            Create Tables
          </button>
        </div>

        <div className="text-sm text-gray-600">
          <strong>Supabase URL:</strong> {import.meta.env.VITE_SUPABASE_URL}<br/>
          <strong>Project ID:</strong> {import.meta.env.VITE_SUPABASE_URL?.split('//')[1]?.split('.')[0]}
        </div>
      </div>
    </div>
  );
};

export default DatabaseTest;
