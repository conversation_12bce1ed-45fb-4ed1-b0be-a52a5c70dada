import { useState } from 'react';
import { motion } from 'framer-motion';
import { Globe, ChevronDown } from 'lucide-react';

type Language = 'tr' | 'en';

interface LanguageSelectorProps {
  selectedLanguage: Language;
  onLanguageChange: (language: Language) => void;
  className?: string;
}

const LanguageSelector = ({ selectedLanguage, onLanguageChange, className = '' }: LanguageSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const languages = [
    { code: 'tr' as Language, name: 'Türkçe', flag: '🇹🇷' },
    { code: 'en' as Language, name: 'English', flag: '🇺🇸' }
  ];

  const selectedLang = languages.find(lang => lang.code === selectedLanguage) || languages[0];

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-coffee focus:border-coffee"
      >
        <div className="flex items-center">
          <Globe className="h-4 w-4 mr-2 text-gray-500" />
          <span className="mr-2">{selectedLang.flag}</span>
          <span>{selectedLang.name}</span>
        </div>
        <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <motion.div
            className="absolute right-0 z-20 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {languages.map((language) => (
              <button
                key={language.code}
                type="button"
                onClick={() => {
                  onLanguageChange(language.code);
                  setIsOpen(false);
                }}
                className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center ${
                  selectedLanguage === language.code 
                    ? 'bg-coffee/5 text-coffee font-medium' 
                    : 'text-gray-700'
                } ${language.code === languages[0].code ? 'rounded-t-md' : ''} ${
                  language.code === languages[languages.length - 1].code ? 'rounded-b-md' : ''
                }`}
              >
                <span className="mr-2">{language.flag}</span>
                <span>{language.name}</span>
                {selectedLanguage === language.code && (
                  <span className="ml-auto text-coffee">✓</span>
                )}
              </button>
            ))}
          </motion.div>
        </>
      )}
    </div>
  );
};

export default LanguageSelector;
