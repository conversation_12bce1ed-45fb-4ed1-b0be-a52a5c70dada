import { useState } from 'react';
import { motion } from 'framer-motion';
import { Globe, AlertCircle } from 'lucide-react';
import RichTextEditor from './RichTextEditor';
import LanguageSelector from './LanguageSelector';
import { useAdminLanguage } from '../../contexts/AdminLanguageContext';

type Language = 'tr' | 'en';

interface MultiLanguageField {
  tr: string;
  en: string;
}

interface MultiLanguageFormProps {
  title: MultiLanguageField;
  content: MultiLanguageField;
  onTitleChange: (language: Language, value: string) => void;
  onContentChange: (language: Language, value: string) => void;
  primaryLanguage: Language;
  onPrimaryLanguageChange: (language: Language) => void;
  showContentEditor?: boolean;
  titleLabel?: string;
  contentLabel?: string;
  titlePlaceholder?: string;
  contentPlaceholder?: string;
}

const MultiLanguageForm = ({
  title,
  content,
  onTitleChange,
  onContentChange,
  primaryLanguage,
  onPrimaryLanguageChange,
  showContentEditor = true,
  titleLabel = 'Title',
  contentLabel = 'Content',
  titlePlaceholder = 'Enter title...',
  contentPlaceholder = 'Enter content...'
}: MultiLanguageFormProps) => {
  const [activeLanguage, setActiveLanguage] = useState<Language>(primaryLanguage);
  const { t } = useAdminLanguage();

  const languages = [
    { code: 'tr' as Language, name: 'Türkçe', flag: '🇹🇷' },
    { code: 'en' as Language, name: 'English', flag: '🇺🇸' }
  ];

  const getLanguageName = (code: Language) => {
    return languages.find(lang => lang.code === code)?.name || code.toUpperCase();
  };

  const getLanguageFlag = (code: Language) => {
    return languages.find(lang => lang.code === code)?.flag || '🌐';
  };

  const isFieldEmpty = (language: Language, field: 'title' | 'content') => {
    const value = field === 'title' ? title[language] : content[language];
    return !value || value.trim() === '';
  };

  const hasEmptyTranslations = () => {
    const otherLanguage = primaryLanguage === 'tr' ? 'en' : 'tr';
    return isFieldEmpty(otherLanguage, 'title') || (showContentEditor && isFieldEmpty(otherLanguage, 'content'));
  };

  return (
    <div className="space-y-6">
      {/* Primary Language Selector */}
      <div>
        <label className="form-label flex items-center">
          <Globe className="h-4 w-4 mr-2" />
          Primary Language
        </label>
        <LanguageSelector
          selectedLanguage={primaryLanguage}
          onLanguageChange={onPrimaryLanguageChange}
          className="w-48"
        />
        <p className="text-xs text-gray-500 mt-1">
          The primary language will be used as the default content
        </p>
      </div>

      {/* Language Tabs */}
      <div>
        <div className="border-b border-gray-200 mb-4">
          <nav className="flex space-x-8">
            {languages.map((language) => (
              <button
                key={language.code}
                type="button"
                onClick={() => setActiveLanguage(language.code)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeLanguage === language.code
                    ? 'border-coffee text-coffee'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{language.flag}</span>
                {language.name}
                {language.code === primaryLanguage && (
                  <span className="ml-2 px-2 py-1 text-xs bg-coffee text-white rounded-full">
                    Primary
                  </span>
                )}
                {language.code !== primaryLanguage && hasEmptyTranslations() && (
                  <AlertCircle className="h-4 w-4 ml-2 text-yellow-500" />
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Content for Active Language */}
        <motion.div
          key={activeLanguage}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-4"
        >
          {/* Title Field */}
          <div>
            <label className="form-label flex items-center justify-between">
              <span>
                {titleLabel} ({getLanguageName(activeLanguage)})
              </span>
              {activeLanguage !== primaryLanguage && isFieldEmpty(activeLanguage, 'title') && (
                <span className="text-xs text-yellow-600 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Translation missing
                </span>
              )}
            </label>
            <input
              type="text"
              value={title[activeLanguage]}
              onChange={(e) => onTitleChange(activeLanguage, e.target.value)}
              className="form-input"
              placeholder={`${titlePlaceholder} (${getLanguageFlag(activeLanguage)} ${getLanguageName(activeLanguage)})`}
            />
          </div>

          {/* Content Field */}
          {showContentEditor && (
            <div>
              <label className="form-label flex items-center justify-between">
                <span>
                  {contentLabel} ({getLanguageName(activeLanguage)})
                </span>
                {activeLanguage !== primaryLanguage && isFieldEmpty(activeLanguage, 'content') && (
                  <span className="text-xs text-yellow-600 flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Translation missing
                  </span>
                )}
              </label>
              <RichTextEditor
                content={content[activeLanguage]}
                onChange={(value) => onContentChange(activeLanguage, value)}
                placeholder={`${contentPlaceholder} (${getLanguageFlag(activeLanguage)} ${getLanguageName(activeLanguage)})`}
                className="mt-1"
              />
            </div>
          )}

          {/* Translation Helper */}
          {activeLanguage !== primaryLanguage && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                {getLanguageFlag(primaryLanguage)} Original ({getLanguageName(primaryLanguage)})
              </h4>
              <div className="space-y-2">
                <div>
                  <p className="text-xs text-blue-700 font-medium">Title:</p>
                  <p className="text-sm text-blue-800 bg-blue-100 p-2 rounded">
                    {title[primaryLanguage] || 'No title in primary language'}
                  </p>
                </div>
                {showContentEditor && (
                  <div>
                    <p className="text-xs text-blue-700 font-medium">Content:</p>
                    <div 
                      className="text-sm text-blue-800 bg-blue-100 p-2 rounded max-h-32 overflow-y-auto prose prose-sm"
                      dangerouslySetInnerHTML={{ 
                        __html: content[primaryLanguage] || 'No content in primary language' 
                      }}
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </motion.div>
      </div>

      {/* Translation Status */}
      {hasEmptyTranslations() && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
            <p className="text-sm text-yellow-800">
              Some translations are missing. Consider adding content in all languages for better user experience.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiLanguageForm;
