import { useState } from 'react';
import { motion } from 'framer-motion';
import { Database, Upload, CheckCircle, AlertCircle } from 'lucide-react';
import { supabase } from '../../lib/supabaseClient';

const SampleDataLoader = () => {
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const sampleMenuItems = [
    {
      name: 'Classic Cheeseburger',
      description: 'Juicy beef patty with melted cheddar, lettuce, tomato, and special sauce',
      price: 12.99,
      category: 'Burgers',
      menu_type: 'restaurant',
      image_url: 'https://images.pexels.com/photos/1633578/pexels-photo-1633578.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: false,
      is_spicy: false,
      is_available: true
    },
    {
      name: 'BBQ Bacon Burger',
      description: 'Smoky beef patty with crispy bacon, cheddar, and tangy BBQ sauce',
      price: 14.99,
      category: 'Burgers',
      menu_type: 'restaurant',
      image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: false,
      is_spicy: true,
      is_available: true
    },
    {
      name: 'Veggie Burger',
      description: 'House-made plant-based patty with avocado, lettuce, tomato, and vegan aioli',
      price: 13.99,
      category: 'Burgers',
      menu_type: 'restaurant',
      image_url: 'https://images.pexels.com/photos/3607284/pexels-photo-3607284.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: true,
      is_spicy: false,
      is_available: true
    },
    {
      name: 'Truffle Fries',
      description: 'Crispy fries tossed with truffle oil, parmesan cheese, and fresh herbs',
      price: 8.99,
      category: 'Sides',
      menu_type: 'restaurant',
      image_url: 'https://images.pexels.com/photos/1893555/pexels-photo-1893555.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: true,
      is_spicy: false,
      is_available: true
    },
    {
      name: 'Chicken Wings',
      description: 'Crispy wings with your choice of buffalo, BBQ, or honey garlic sauce',
      price: 11.99,
      category: 'Appetizers',
      menu_type: 'restaurant',
      image_url: 'https://images.pexels.com/photos/60616/fried-chicken-chicken-fried-crunchy-60616.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: false,
      is_spicy: true,
      is_available: true
    },
    {
      name: 'Iced Coffee',
      description: 'Smooth cold brew coffee served over ice with your choice of milk',
      price: 4.99,
      category: 'Drinks',
      menu_type: 'cafe',
      image_url: 'https://images.pexels.com/photos/2638019/pexels-photo-2638019.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: true,
      is_spicy: false,
      is_available: true
    },
    {
      name: 'Oreo Milkshake',
      description: 'Creamy vanilla milkshake blended with Oreo cookies and topped with whipped cream',
      price: 6.99,
      category: 'Drinks',
      menu_type: 'cafe',
      image_url: 'https://images.pexels.com/photos/3727250/pexels-photo-3727250.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: true,
      is_spicy: false,
      is_available: true
    },
    {
      name: 'Chocolate Brownie',
      description: 'Rich, fudgy brownie served warm with vanilla ice cream',
      price: 7.99,
      category: 'Desserts',
      menu_type: 'cafe',
      image_url: 'https://images.pexels.com/photos/1055272/pexels-photo-1055272.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      is_vegetarian: true,
      is_spicy: false,
      is_available: true
    }
  ];

  const sampleBlogPosts = [
    {
      title: 'The Secret to Our Juicy Burgers',
      content: `<p>At Shebo's Burger, we take pride in serving the juiciest, most flavorful burgers in town. But what's our secret? It all starts with quality ingredients.</p><p>We use only premium beef that's ground fresh daily. Our special blend of chuck and brisket provides the perfect fat content for a juicy, flavorful patty. We season our patties with a proprietary mix of spices that enhances the natural flavor of the beef without overwhelming it.</p><p>Another key factor is how we cook our burgers. We use a flat-top grill that sears the patties perfectly, locking in juices and creating a delicious crust. We always cook to order, ensuring each burger reaches your table at peak deliciousness.</p><p>Finally, we pay attention to the details. Our buns are baked fresh daily, and we toast them lightly for the perfect texture. Our toppings are always fresh and high-quality, from crisp lettuce to vine-ripened tomatoes.</p><p>Come taste the difference at Shebo's Burger!</p>`,
      excerpt: 'Discover what makes our burgers stand out from the competition - from ingredient selection to cooking techniques.',
      image_url: 'https://images.pexels.com/photos/3219547/pexels-photo-3219547.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      author: 'Chef Michael',
      category: 'Cooking Tips',
      published: true
    },
    {
      title: 'Upcoming Summer Special Menu',
      content: `<p>Summer is just around the corner, and we're excited to announce our new seasonal menu! We've created a selection of fresh, vibrant dishes that capture the essence of summer.</p><p>Our new Tropical Paradise Burger features a juicy beef patty topped with grilled pineapple, crispy bacon, and a zesty mango sauce. It's like a vacation in every bite! For seafood lovers, our Grilled Shrimp Burger with avocado and citrus slaw is a must-try.</p><p>We're also introducing some refreshing new sides. Our Watermelon Feta Salad is the perfect light accompaniment to any burger, and our Sweet Corn Fritters with chili-lime dip are already becoming a customer favorite.</p><p>To help you beat the heat, we've created new summer beverages including a Strawberry Basil Lemonade and a Coconut Cold Brew that are both refreshing and delicious.</p><p>Our summer menu launches on June 1st and will be available through August. Don't miss your chance to try these seasonal specialties!</p>`,
      excerpt: 'Get a sneak peek at our upcoming summer menu featuring seasonal ingredients and tropical flavors.',
      image_url: 'https://images.pexels.com/photos/1600711/pexels-photo-1600711.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      author: 'Sarah Williams',
      category: 'Menu Updates',
      published: true
    },
    {
      title: 'The History of Shebo\'s Burger',
      content: `<p>What started as a dream in 2015 has grown into the beloved restaurant you know today. Shebo's Burger began as a small food truck parked outside local events, serving simple but delicious burgers to hungry customers.</p><p>Our founder, Michael Shebo, had a vision: to create burgers that reminded people of home-cooked meals, using only the freshest ingredients and time-tested recipes passed down through generations.</p><p>The food truck was an instant hit. Word spread quickly about the "burger truck with the amazing food," and soon we had lines of customers waiting for their turn to try our signature burgers.</p><p>In 2018, we opened our first brick-and-mortar location, expanding our menu to include more options while staying true to our core values of quality and flavor. Today, we're proud to serve our community with the same passion and dedication that started it all.</p><p>Thank you for being part of our journey. Here's to many more years of great food and happy customers!</p>`,
      excerpt: 'Learn about how Shebo\'s Burger grew from a small food truck to the beloved restaurant it is today.',
      image_url: 'https://images.pexels.com/photos/2983101/pexels-photo-2983101.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      author: 'Michael Shebo',
      category: 'Our Story',
      published: true
    }
  ];

  const sampleContentSections = [
    {
      name: 'contact',
      title: 'İletişim & Konum',
      content: 'Unutulmaz bir lezzet deneyimi için premium malzemelerle el yapımı burgerler keşfedin.',
      display_order: 1,
      section: 'contact',
      page: 'home'
    },
    {
      name: 'contact-address',
      title: 'Adres',
      content: 'Bostanlı Mahallesi, İzmir',
      display_order: 1,
      section: 'contact',
      page: 'global'
    },
    {
      name: 'contact-phone',
      title: 'Telefon',
      content: '+90 232 123 45 67',
      display_order: 2,
      section: 'contact',
      page: 'global'
    },
    {
      name: 'contact-email',
      title: 'E-posta',
      content: '<EMAIL>',
      display_order: 3,
      section: 'contact',
      page: 'global'
    },
    {
      name: 'contact-hours-weekdays',
      title: 'Hafta İçi Saatleri',
      content: 'Pazartesi - Cuma: 11:00 - 23:00',
      display_order: 4,
      section: 'contact',
      page: 'global'
    },
    {
      name: 'contact-hours-weekend',
      title: 'Hafta Sonu Saatleri',
      content: 'Cumartesi - Pazar: 10:00 - 24:00',
      display_order: 5,
      section: 'contact',
      page: 'global'
    },
    {
      name: 'footer-address',
      title: 'Footer Adres',
      content: 'Bostanlı Mahallesi, İzmir',
      display_order: 1,
      section: 'footer',
      page: 'global'
    },
    {
      name: 'footer-phone',
      title: 'Footer Telefon',
      content: '+90 232 123 45 67',
      display_order: 2,
      section: 'footer',
      page: 'global'
    },
    {
      name: 'footer-email',
      title: 'Footer E-posta',
      content: '<EMAIL>',
      display_order: 3,
      section: 'footer',
      page: 'global'
    },
    {
      name: 'footer-description',
      title: 'Footer Açıklama',
      content: 'Shebo\'s Burger, 2015 yılından beri en taze malzemelerle en lezzetli burgerleri sunuyor. Kalite ve lezzet konusundaki kararlılığımız hiç değişmedi.',
      display_order: 4,
      section: 'footer',
      page: 'global'
    }
  ];

  const loadSampleData = async () => {
    setLoading(true);
    setStatus('idle');
    setMessage('');

    try {
      // Insert menu items
      const { error: menuError } = await supabase
        .from('menu_items')
        .insert(sampleMenuItems);

      if (menuError) throw menuError;

      // Insert blog posts
      const { error: blogError } = await supabase
        .from('blog_posts')
        .insert(sampleBlogPosts);

      if (blogError) throw blogError;

      // Insert content sections
      const { error: contentError } = await supabase
        .from('content')
        .insert(sampleContentSections);

      if (contentError) throw contentError;

      setStatus('success');
      setMessage('Sample data loaded successfully! You can now see menu items, blog posts, and contact information on the website.');
    } catch (error) {
      console.error('Error loading sample data:', error);
      setStatus('error');
      setMessage('Failed to load sample data. This might be because the data already exists or there was a database error.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      className="bg-white rounded-lg shadow-md p-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <div className="flex items-center mb-4">
        <Database className="h-6 w-6 text-coffee mr-2" />
        <h3 className="text-xl font-bold">Sample Data Loader</h3>
      </div>

      <p className="text-gray-600 mb-6">
        Load sample menu items and blog posts to populate your website with demo content.
        This is useful for testing and demonstration purposes.
      </p>

      <div className="space-y-4">
        <div className="text-sm text-gray-500">
          <p><strong>This will add:</strong></p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>8 sample menu items (burgers, sides, drinks, desserts)</li>
            <li>3 sample blog posts with rich content</li>
            <li>10 content sections (contact info, footer content)</li>
          </ul>
        </div>

        {status !== 'idle' && (
          <div className={`p-4 rounded-md flex items-center ${
            status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {status === 'success' ? (
              <CheckCircle className="h-5 w-5 mr-2" />
            ) : (
              <AlertCircle className="h-5 w-5 mr-2" />
            )}
            <span className="text-sm">{message}</span>
          </div>
        )}

        <button
          onClick={loadSampleData}
          disabled={loading}
          className="btn bg-coffee text-white hover:bg-coffee/90 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Loading Data...
            </>
          ) : (
            <>
              <Upload className="h-5 w-5 mr-2" />
              Load Sample Data
            </>
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default SampleDataLoader;
