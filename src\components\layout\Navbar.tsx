import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Globe } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { motion } from 'framer-motion';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const { session } = useAuth();
  const { language, setLanguage, t } = useLanguage();
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  return (
    <nav
      className={`fixed w-full z-50 transition-all duration-300 ${
        scrolled ? 'bg-coffee text-cream py-2 shadow-md' : 'bg-transparent text-coffee py-4'
      }`}
    >
      <div className="container-custom flex justify-between items-center">
        <Link to="/" className="flex items-center">
          <img src="/logo.svg" alt="Shebo's Sandwich & Burger" className="h-12 w-12 mr-3" />
          <span className="font-serif font-bold text-2xl">Shebo's Sandwich & Burger</span>
        </Link>

        {/* Desktop Nav */}
        <div className="hidden md:flex items-center space-x-6">
          <NavLink to="/" label={t('nav.home')} scrolled={scrolled} />
          <NavLink to="/menu" label={t('nav.menu')} scrolled={scrolled} />
          <NavLink to="/blog" label={t('nav.blog')} scrolled={scrolled} />

          {/* Language Switcher */}
          <div className="relative">
            <button
              onClick={() => setLanguage(language === 'tr' ? 'en' : 'tr')}
              className={`flex items-center space-x-1 px-3 py-2 rounded-md transition-colors ${
                scrolled
                  ? 'text-cream hover:bg-cream/10'
                  : 'text-coffee hover:bg-coffee/10'
              }`}
            >
              <Globe className="h-4 w-4" />
              <span className="text-sm font-medium">{language.toUpperCase()}</span>
            </button>
          </div>

          {session ? (
            <Link
              to="/admin"
              className={`btn ${scrolled ? 'btn-outline border-cream text-cream' : 'btn-outline'} py-2 px-4`}
            >
              {t('nav.admin')}
            </Link>
          ) : (
            <Link
              to="/login"
              className={`btn ${scrolled ? 'btn-outline border-cream text-cream' : 'btn-outline'} py-2 px-4`}
            >
              {t('admin.login')}
            </Link>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden focus:outline-none"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Mobile Menu */}
      {isOpen && (
        <motion.div
          className="md:hidden bg-coffee text-cream"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="container-custom py-4 flex flex-col space-y-3">
            <MobileNavLink to="/" label={t('nav.home')} />
            <MobileNavLink to="/menu" label={t('nav.menu')} />
            <MobileNavLink to="/blog" label={t('nav.blog')} />

            {/* Mobile Language Switcher */}
            <button
              onClick={() => setLanguage(language === 'tr' ? 'en' : 'tr')}
              className="flex items-center space-x-2 py-2 px-4 rounded-md hover:bg-mocha/50 transition-colors"
            >
              <Globe className="h-4 w-4" />
              <span>{language === 'tr' ? 'Türkçe' : 'English'}</span>
            </button>

            {session ? (
              <MobileNavLink to="/admin" label={t('nav.admin')} />
            ) : (
              <MobileNavLink to="/login" label={t('admin.login')} />
            )}
          </div>
        </motion.div>
      )}
    </nav>
  );
};

// NavLink Component for Desktop
const NavLink = ({ to, label, scrolled }: { to: string; label: string; scrolled: boolean }) => {
  const location = useLocation();
  const isActive = location.pathname === to;

  return (
    <Link
      to={to}
      className={`relative font-medium transition-colors ${
        scrolled
          ? 'text-cream hover:text-cream/80'
          : 'text-coffee hover:text-coffee/80'
      } ${isActive ? 'font-bold' : ''}`}
    >
      {label}
      {isActive && (
        <span className={`absolute -bottom-1 left-0 w-full h-0.5 ${
          scrolled ? 'bg-cream' : 'bg-coffee'
        }`} />
      )}
    </Link>
  );
};

// Mobile NavLink Component
const MobileNavLink = ({ to, label }: { to: string; label: string }) => {
  const location = useLocation();
  const isActive = location.pathname === to;

  return (
    <Link
      to={to}
      className={`py-2 px-4 block rounded-md transition-colors ${
        isActive
          ? 'bg-mocha font-bold'
          : 'hover:bg-mocha/50'
      }`}
    >
      {label}
    </Link>
  );
};

export default Navbar;