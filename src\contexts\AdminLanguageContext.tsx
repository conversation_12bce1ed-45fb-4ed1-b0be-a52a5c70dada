import React, { createContext, useContext, useState, useEffect } from 'react';

export type AdminLanguage = 'tr' | 'en';

interface AdminLanguageContextType {
  adminLanguage: AdminLanguage;
  setAdminLanguage: (lang: AdminLanguage) => void;
  t: (key: string) => string;
}

const AdminLanguageContext = createContext<AdminLanguageContextType | undefined>(undefined);

// Admin-specific translations
const adminTranslations = {
  tr: {
    // Admin Navigation
    'admin.dashboard': 'Kontrol Paneli',
    'admin.menu': '<PERSON><PERSON>im<PERSON>',
    'admin.blog': 'Blog Yönetimi',
    'admin.content': 'İçerik Yönetimi',
    'admin.contactSubmissions': '<PERSON>letişim <PERSON>',
    'admin.settings': 'Ayarlar',
    'admin.viewWebsite': 'Web Sitesini Görüntüle',
    'admin.signOut': '<PERSON>ık<PERSON>ş Ya<PERSON>',
    'admin.language': 'Dil',

    // Dashboard
    'admin.title': 'Yönetim Paneli',
    'admin.welcome': 'Shebo\'s Burger yönetim paneline hoş geldiniz.',
    'admin.stats.menuItems': 'Menü Öğeleri',
    'admin.stats.blogPosts': 'Blog Yazıları',
    'admin.stats.contentSections': 'İçerik Bölümleri',
    'admin.quickActions': 'Hızlı İşlemler',
    'admin.manageMenu': 'Menü Yönet',
    'admin.manageMenu.desc': 'Menü öğelerini ve kategorilerini ekle, düzenle veya sil',
    'admin.manageBlog': 'Blog Yönet',
    'admin.manageBlog.desc': 'Blog yazıları oluştur ve düzenle',
    'admin.manageContent': 'İçerik Yönet',
    'admin.manageContent.desc': 'Ana sayfa ve diğer içerik bölümlerini güncelle',
    'admin.contactSubmissions.desc': 'İletişim formu gönderimlerini görüntüle ve yönet',
    'admin.settings.desc': 'Şifreleri ve kullanıcı hesaplarını yönet',
    'admin.menuImportTool': 'Menü İçe Aktarma Aracı',
    'admin.databaseTest': 'Veritabanı Testi',
    'admin.devTools': 'Geliştirme Araçları',
    'admin.goTo': 'Git',

    // Settings
    'admin.changePassword': 'Şifre Değiştir',
    'admin.userManagement': 'Kullanıcı Yönetimi',
    'admin.currentPassword': 'Mevcut Şifre',
    'admin.newPassword': 'Yeni Şifre',
    'admin.confirmPassword': 'Şifreyi Onayla',
    'admin.updatePassword': 'Şifreyi Güncelle',
    'admin.createUser': 'Kullanıcı Oluştur',
    'admin.email': 'E-posta',
    'admin.password': 'Şifre',
    'admin.role': 'Rol',
    'admin.admin': 'Yönetici',
    'admin.editor': 'Editör',
    'admin.existingUsers': 'Mevcut Kullanıcılar',
    'admin.user': 'Kullanıcı',
    'admin.lastSignIn': 'Son Giriş',
    'admin.actions': 'İşlemler',
    'admin.never': 'Hiçbir zaman',
    'admin.delete': 'Sil',

    // Contact Submissions
    'admin.contactSubmissions.title': 'İletişim Mesajları',
    'admin.contactSubmissions.subtitle': 'İletişim formu gönderimlerini görüntüle ve yönet',
    'admin.contactSubmissions.noSubmissions': 'Henüz iletişim mesajı yok.',
    'admin.contactSubmissions.loading': 'Mesajlar yükleniyor...',
    'admin.contactSubmissions.details': 'İletişim Mesajı Detayları',
    'admin.contactSubmissions.name': 'Ad Soyad',
    'admin.contactSubmissions.email': 'E-posta',
    'admin.contactSubmissions.phone': 'Telefon',
    'admin.contactSubmissions.message': 'Mesaj',
    'admin.contactSubmissions.submitted': 'Gönderilme Tarihi',
    'admin.contactSubmissions.recaptchaScore': 'reCAPTCHA Puanı',
    'admin.contactSubmissions.highTrust': 'Yüksek Güven',
    'admin.contactSubmissions.mediumTrust': 'Orta Güven',
    'admin.contactSubmissions.lowTrust': 'Düşük Güven',
    'admin.contactSubmissions.view': 'Görüntüle',
    'admin.contactSubmissions.close': 'Kapat',
    'admin.contactSubmissions.deleteConfirm': 'Bu mesajı silmek istediğinizden emin misiniz?',

    // Common
    'admin.save': 'Kaydet',
    'admin.cancel': 'İptal',
    'admin.edit': 'Düzenle',
    'admin.addNew': 'Yeni Ekle',
    'admin.loading': 'Yükleniyor...',
    'admin.delete': 'Sil',
    'admin.email': 'E-posta',
    'admin.password': 'Şifre',

    // Blog Management
    'admin.blog.title': 'Blog Yönetimi',
    'admin.blog.subtitle': 'Blog yazıları oluştur, düzenle ve yönet',
    'admin.blog.newPost': 'Yeni Yazı',
    'admin.blog.editPost': 'Yazıyı Düzenle',
    'admin.blog.createPost': 'Yeni Blog Yazısı Oluştur',
    'admin.blog.searchPlaceholder': 'Blog yazılarını ara...',
    'admin.blog.noPostsFound': 'Blog yazısı bulunamadı. Farklı bir arama deneyin veya yeni bir yazı oluşturun.',
    'admin.blog.loadingPosts': 'Blog yazıları yükleniyor...',
    'admin.blog.title.column': 'Başlık',
    'admin.blog.category': 'Kategori',
    'admin.blog.author': 'Yazar',
    'admin.blog.date': 'Tarih',
    'admin.blog.status': 'Durum',
    'admin.blog.actions': 'İşlemler',
    'admin.blog.published': 'Yayınlandı',
    'admin.blog.draft': 'Taslak',
    'admin.blog.publish': 'Yayınla',
    'admin.blog.unpublish': 'Yayından Kaldır',
    'admin.blog.deleteConfirm': 'Bu blog yazısını silmek istediğinizden emin misiniz?',
    'admin.blog.excerpt': 'Özet',
    'admin.blog.content': 'İçerik',
    'admin.blog.featuredImage': 'Öne Çıkan Görsel URL',
    'admin.blog.publishImmediately': 'Hemen yayınla',
    'admin.blog.updatePost': 'Yazıyı Güncelle',
    'admin.blog.createPost.button': 'Yazı Oluştur',

    // Content Management
    'admin.content.title': 'İçerik Yönetimi',
    'admin.content.subtitle': 'Web sitesi içerik bölümlerini düzenle',
    'admin.content.loading': 'İçerik bölümleri yükleniyor...',
    'admin.content.noSections': 'İçerik bölümü bulunamadı.',
    'admin.content.editSection': 'İçerik Bölümünü Düzenle',
    'admin.content.editContent': 'İçeriği Düzenle',
    'admin.content.title.field': 'Başlık',
    'admin.content.content.field': 'İçerik',
    'admin.content.imageUrl': 'Görsel URL',
    'admin.content.sectionId': 'Bölüm ID',
    'admin.content.page': 'Sayfa',
    'admin.content.displayOrder': 'Görüntüleme Sırası',
    'admin.content.currentImage': 'Mevcut Görsel',
    'admin.content.saveChanges': 'Değişiklikleri Kaydet',
    'admin.content.page.home': 'Ana Sayfa',
    'admin.content.page.about': 'Hakkımızda',
    'admin.content.page.contact': 'İletişim',
  },
  en: {
    // Admin Navigation
    'admin.dashboard': 'Dashboard',
    'admin.menu': 'Menu Management',
    'admin.blog': 'Blog Management',
    'admin.content': 'Content Management',
    'admin.contactSubmissions': 'Contact Submissions',
    'admin.settings': 'Settings',
    'admin.viewWebsite': 'View Website',
    'admin.signOut': 'Sign Out',
    'admin.language': 'Language',

    // Dashboard
    'admin.title': 'Admin Dashboard',
    'admin.welcome': 'Welcome to Shebo\'s Burger admin panel.',
    'admin.stats.menuItems': 'Menu Items',
    'admin.stats.blogPosts': 'Blog Posts',
    'admin.stats.contentSections': 'Content Sections',
    'admin.quickActions': 'Quick Actions',
    'admin.manageMenu': 'Manage Menu',
    'admin.manageMenu.desc': 'Add, edit, or delete menu items and categories',
    'admin.manageBlog': 'Manage Blog',
    'admin.manageBlog.desc': 'Create and edit blog posts',
    'admin.manageContent': 'Manage Content',
    'admin.manageContent.desc': 'Update homepage and other content sections',
    'admin.contactSubmissions.desc': 'View and manage contact form submissions',
    'admin.settings.desc': 'Manage passwords and user accounts',
    'admin.menuImportTool': 'Menu Import Tool',
    'admin.databaseTest': 'Database Test',
    'admin.devTools': 'Development Tools',
    'admin.goTo': 'Go to',

    // Settings
    'admin.changePassword': 'Change Password',
    'admin.userManagement': 'User Management',
    'admin.currentPassword': 'Current Password',
    'admin.newPassword': 'New Password',
    'admin.confirmPassword': 'Confirm Password',
    'admin.updatePassword': 'Update Password',
    'admin.createUser': 'Create User',
    'admin.email': 'Email',
    'admin.password': 'Password',
    'admin.role': 'Role',
    'admin.admin': 'Admin',
    'admin.editor': 'Editor',
    'admin.existingUsers': 'Existing Users',
    'admin.user': 'User',
    'admin.lastSignIn': 'Last Sign In',
    'admin.actions': 'Actions',
    'admin.never': 'Never',
    'admin.delete': 'Delete',

    // Contact Submissions
    'admin.contactSubmissions.title': 'Contact Submissions',
    'admin.contactSubmissions.subtitle': 'View and manage contact form submissions',
    'admin.contactSubmissions.noSubmissions': 'No contact submissions yet.',
    'admin.contactSubmissions.loading': 'Loading submissions...',
    'admin.contactSubmissions.details': 'Contact Submission Details',
    'admin.contactSubmissions.name': 'Name',
    'admin.contactSubmissions.email': 'Email',
    'admin.contactSubmissions.phone': 'Phone',
    'admin.contactSubmissions.message': 'Message',
    'admin.contactSubmissions.submitted': 'Submitted',
    'admin.contactSubmissions.recaptchaScore': 'reCAPTCHA Score',
    'admin.contactSubmissions.highTrust': 'High Trust',
    'admin.contactSubmissions.mediumTrust': 'Medium Trust',
    'admin.contactSubmissions.lowTrust': 'Low Trust',
    'admin.contactSubmissions.view': 'View',
    'admin.contactSubmissions.close': 'Close',
    'admin.contactSubmissions.deleteConfirm': 'Are you sure you want to delete this submission?',

    // Common
    'admin.save': 'Save',
    'admin.cancel': 'Cancel',
    'admin.edit': 'Edit',
    'admin.addNew': 'Add New',
    'admin.loading': 'Loading...',
    'admin.delete': 'Delete',
    'admin.email': 'Email',
    'admin.password': 'Password',

    // Blog Management
    'admin.blog.title': 'Blog Management',
    'admin.blog.subtitle': 'Create, edit, and manage blog posts',
    'admin.blog.newPost': 'New Post',
    'admin.blog.editPost': 'Edit Post',
    'admin.blog.createPost': 'Create New Blog Post',
    'admin.blog.searchPlaceholder': 'Search blog posts...',
    'admin.blog.noPostsFound': 'No blog posts found. Try a different search or create a new post.',
    'admin.blog.loadingPosts': 'Loading blog posts...',
    'admin.blog.title.column': 'Title',
    'admin.blog.category': 'Category',
    'admin.blog.author': 'Author',
    'admin.blog.date': 'Date',
    'admin.blog.status': 'Status',
    'admin.blog.actions': 'Actions',
    'admin.blog.published': 'Published',
    'admin.blog.draft': 'Draft',
    'admin.blog.publish': 'Publish',
    'admin.blog.unpublish': 'Unpublish',
    'admin.blog.deleteConfirm': 'Are you sure you want to delete this blog post?',
    'admin.blog.excerpt': 'Excerpt',
    'admin.blog.content': 'Content',
    'admin.blog.featuredImage': 'Featured Image URL',
    'admin.blog.publishImmediately': 'Publish immediately',
    'admin.blog.updatePost': 'Update Post',
    'admin.blog.createPost.button': 'Create Post',

    // Content Management
    'admin.content.title': 'Content Management',
    'admin.content.subtitle': 'Edit website content sections',
    'admin.content.loading': 'Loading content sections...',
    'admin.content.noSections': 'No content sections found.',
    'admin.content.editSection': 'Edit Content Section',
    'admin.content.editContent': 'Edit Content',
    'admin.content.title.field': 'Title',
    'admin.content.content.field': 'Content',
    'admin.content.imageUrl': 'Image URL',
    'admin.content.sectionId': 'Section ID',
    'admin.content.page': 'Page',
    'admin.content.displayOrder': 'Display Order',
    'admin.content.currentImage': 'Current Image',
    'admin.content.saveChanges': 'Save Changes',
    'admin.content.page.home': 'Home Page',
    'admin.content.page.about': 'About Page',
    'admin.content.page.contact': 'Contact Page',
  }
};

export function AdminLanguageProvider({ children }: { children: React.ReactNode }) {
  const [adminLanguage, setAdminLanguage] = useState<AdminLanguage>('tr'); // Default to Turkish

  useEffect(() => {
    const savedAdminLanguage = localStorage.getItem('adminLanguage') as AdminLanguage;
    if (savedAdminLanguage && (savedAdminLanguage === 'tr' || savedAdminLanguage === 'en')) {
      setAdminLanguage(savedAdminLanguage);
    }
  }, []);

  const handleSetAdminLanguage = (lang: AdminLanguage) => {
    setAdminLanguage(lang);
    localStorage.setItem('adminLanguage', lang);
  };

  const t = (key: string): string => {
    return adminTranslations[adminLanguage][key as keyof typeof adminTranslations[typeof adminLanguage]] || key;
  };

  return (
    <AdminLanguageContext.Provider value={{ adminLanguage, setAdminLanguage: handleSetAdminLanguage, t }}>
      {children}
    </AdminLanguageContext.Provider>
  );
}

export function useAdminLanguage() {
  const context = useContext(AdminLanguageContext);
  if (context === undefined) {
    throw new Error('useAdminLanguage must be used within an AdminLanguageProvider');
  }
  return context;
}
