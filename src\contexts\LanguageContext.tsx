import React, { createContext, useContext, useState, useEffect } from 'react';

export type Language = 'tr' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const translations = {
  tr: {
    // Navigation
    'nav.home': 'Ana Sayfa',
    'nav.menu': 'Menü',
    'nav.blog': 'Blog',
    'nav.contact': '<PERSON>let<PERSON><PERSON><PERSON>',
    'nav.admin': 'Yönetim',

    // Homepage
    'hero.title': 'Burger Mükemmelliğinin <PERSON>ı<PERSON> Çıkarın',
    'hero.subtitle': 'Unutulmaz bir lezzet deneyimi için premium malzemelerle el yapımı burgerler.',
    'hero.cta': '<PERSON>üyü Görüntüle',
    'google.cta': 'Yol Tari<PERSON> Al',

    'about.title': 'Hikaye<PERSON><PERSON>',
    'about.content': 'She<PERSON>\'s Burger 2015 yılında basit bir misyonla kuruldu: sadece en taze malzemeleri kullanarak en lezzetli, yüks<PERSON> kaliteli burgerleri yaratmak. Küçük bir seyyar satış arabası olarak başlayan yolculuğumuz, sevilen yerel bir restorana dönüştü, ancak kalite ve lezzet konusundaki kararlılığımız hiç değişmedi.',

    'testimonials.title': 'Müşteri Yorumları',
    'testimonials.1.text': 'Şehirdeki en iyi burger! Malzemeler her zaman taze ve lezzet inanılmaz.',
    'testimonials.1.author': 'Ahmet Yılmaz',
    'testimonials.2.text': 'Harika atmosfer ve mükemmel servis. Kesinlikle tavsiye ederim!',
    'testimonials.2.author': 'Ayşe Demir',
    'testimonials.3.text': 'Vegetaryen seçenekleri de çok lezzetli. Herkese hitap eden bir yer.',
    'testimonials.3.author': 'Mehmet Kaya',

    'contact.title': 'İletişim & Konum',
    'contact.address': 'Adres',
    'contact.phone': 'Telefon',
    'contact.email': 'Mail',
    'contact.hours': 'Açılış Saatleri',
    'contact.hours.weekdays': 'Pazartesi - Cuma: 11:00 - 23:00',
    'contact.hours.weekend': 'Cumartesi - Pazar: 10:00 - 24:00',

    // Contact Form
    'contact.form.title': 'Bizimle İletişime Geçin',
    'contact.form.subtitle': 'Sorularınız, önerileriniz veya rezervasyon talepleriniz için bize ulaşın.',
    'contact.form.name': 'Ad Soyad',
    'contact.form.email': 'E-posta',
    'contact.form.phone': 'Telefon',
    'contact.form.message': 'Mesaj',
    'contact.form.send': 'Mesaj Gönder',
    'contact.form.sending': 'Gönderiliyor...',
    'contact.form.success': 'Mesajınız başarıyla gönderildi! En kısa sürede size dönüş yapacağız.',
    'contact.form.error': 'Mesaj gönderilirken bir hata oluştu. Lütfen tekrar deneyin.',
    'contact.form.nameRequired': 'Ad soyad gereklidir',
    'contact.form.nameMinLength': 'Ad soyad en az 2 karakter olmalıdır',
    'contact.form.namePlaceholder': 'Adınızı ve soyadınızı girin',
    'contact.form.emailRequired': 'E-posta adresi gereklidir',
    'contact.form.emailInvalid': 'Geçerli bir e-posta adresi girin',
    'contact.form.emailPlaceholder': '<EMAIL>',
    'contact.form.phoneRequired': 'Telefon numarası gereklidir',
    'contact.form.phoneInvalid': 'Geçerli bir telefon numarası girin',
    'contact.form.phonePlaceholder': '+90 5XX XXX XX XX',
    'contact.form.messageRequired': 'Mesaj gereklidir',
    'contact.form.messageMinLength': 'Mesaj en az 10 karakter olmalıdır',
    'contact.form.messagePlaceholder': 'Mesajınızı buraya yazın...',
    'contact.form.recaptcha': 'Bu site reCAPTCHA ile korunmaktadır.',

    // Menu
    'menu.title': 'Menümüz',
    'menu.featured': 'Öne Çıkan Lezzetler',
    'menu.restaurant': 'Burger & Hotdog',
    'menu.cafe': 'Cafe',
    'menu.search': 'Menüde ara...',
    'menu.filter.all': 'Tümü',
    'menu.filter.vegetarian': 'Vejetaryen',
    'menu.filter.spicy': 'Acılı',
    'menu.currency': '₺',

    // Menu Categories
    'category.hamburgers': 'Hamburgerler',
    'category.chicken-burgers': 'Chicken Burgerler',
    'category.hot-dogs': 'Hot Doglar',
    'category.hot-coffees': 'Sıcak Kahveler',
    'category.brew-coffees': 'Demleme Kahveler',
    'category.cold-coffees': 'Soğuk Kahveler',
    'category.teas-drinks': 'Çaylar & Diğer İçecekler',
    'category.snacks': 'Tuzlu / Tatlı Atıştırmalıklar',

    // Blog
    'blog.title': 'Blog',
    'blog.readMore': 'Devamını Oku',
    'blog.shareOn': 'Paylaş:',
    'blog.category': 'Kategori',
    'blog.author': 'Yazar',
    'blog.date': 'Tarih',

    // Footer
    'footer.about': 'Hakkımızda',
    'footer.description': 'Shebo\'s Burger, 2015 yılından beri en taze malzemelerle en lezzetli burgerleri sunuyor. Kalite ve lezzet konusundaki kararlılığımız hiç değişmedi.',
    'footer.links': 'Hızlı Bağlantılar',
    'footer.hours': 'Açılış Saatleri',
    'footer.hours.weekdays': 'Pazartesi - Cuma',
    'footer.hours.saturday': 'Cumartesi',
    'footer.hours.sunday': 'Pazar',
    'footer.contact': 'İletişim',
    'footer.address': 'Bostanlı Mahallesi, İzmir',
    'footer.phone': '+90 232 123 45 67',
    'footer.email': '<EMAIL>',
    'footer.copyright': 'Tüm Hakları Saklıdır.',
    'footer.privacy': 'Gizlilik Politikası',
    'footer.terms': 'Kullanım Şartları',

    // Admin
    'admin.title': 'Yönetim Paneli',
    'admin.login': 'Giriş Yap',
    'admin.logout': 'Çıkış Yap',
    'admin.email': 'E-posta',
    'admin.password': 'Şifre',
    'admin.dashboard': 'Kontrol Paneli',
    'admin.menu': 'Menü Yönetimi',
    'admin.blog': 'Blog Yönetimi',
    'admin.content': 'İçerik Yönetimi',
    'admin.addNew': 'Yeni Ekle',
    'admin.edit': 'Düzenle',
    'admin.delete': 'Sil',
    'admin.save': 'Kaydet',
    'admin.cancel': 'İptal',
    'admin.loading': 'Yükleniyor...',
    'admin.welcome': 'Shebo\'s Burger yönetim paneline hoş geldiniz.',
    'admin.stats.menuItems': 'Menü Öğeleri',
    'admin.stats.blogPosts': 'Blog Yazıları',
    'admin.stats.contentSections': 'İçerik Bölümleri',
    'admin.quickActions': 'Hızlı İşlemler',
    'admin.manageMenu': 'Menü Yönet',
    'admin.manageMenu.desc': 'Menü öğelerini ve kategorilerini ekle, düzenle veya sil',
    'admin.manageBlog': 'Blog Yönet',
    'admin.manageBlog.desc': 'Blog yazıları oluştur ve düzenle',
    'admin.manageContent': 'İçerik Yönet',
    'admin.manageContent.desc': 'Ana sayfa ve diğer içerik bölümlerini güncelle',
    'admin.devTools': 'Geliştirme Araçları',
    'admin.signOut': 'Çıkış Yap',
    'admin.goTo': 'Git',
    'admin.viewWebsite': 'Web Sitesini Görüntüle',
    'admin.settings': 'Ayarlar',
    'admin.contactSubmissions': 'İletişim Mesajları',
    'admin.contactSubmissions.desc': 'İletişim formu gönderimlerini görüntüle ve yönet',
    'admin.settings.desc': 'Şifreleri ve kullanıcı hesaplarını yönet',
    'admin.menuImportTool': 'Menü İçe Aktarma Aracı',
    'admin.databaseTest': 'Veritabanı Testi',
    'admin.changePassword': 'Şifre Değiştir',
    'admin.userManagement': 'Kullanıcı Yönetimi',
    'admin.currentPassword': 'Mevcut Şifre',
    'admin.newPassword': 'Yeni Şifre',
    'admin.confirmPassword': 'Şifreyi Onayla',
    'admin.updatePassword': 'Şifreyi Güncelle',
    'admin.createUser': 'Kullanıcı Oluştur',
    'admin.role': 'Rol',
    'admin.admin': 'Yönetici',
    'admin.editor': 'Editör',
    'admin.existingUsers': 'Mevcut Kullanıcılar',
    'admin.user': 'Kullanıcı',
    'admin.lastSignIn': 'Son Giriş',
    'admin.actions': 'İşlemler',
    'admin.never': 'Hiçbir zaman',
    'admin.language': 'Dil',
    'admin.turkish': 'Türkçe',
    'admin.english': 'English',

    // Menu Management
    'admin.menu.title': 'Menü Yönetimi',
    'admin.menu.subtitle': 'Menü öğelerini ekle, düzenle ve sil',
    'admin.menu.addItem': 'Öğe Ekle',
    'admin.menu.editItem': 'Menü Öğesini Düzenle',
    'admin.menu.addNewItem': 'Yeni Menü Öğesi Ekle',
    'admin.menu.restaurant': 'Restoran',
    'admin.menu.cafe': 'Kafe',
    'admin.menu.search': 'Menü öğelerini ara...',
    'admin.menu.noItems': 'Menü öğesi bulunamadı. Farklı bir arama deneyin veya yeni öğe ekleyin.',
    'admin.menu.loading': 'Menü öğeleri yükleniyor...',
    'admin.menu.name': 'Ad',
    'admin.menu.category': 'Kategori',
    'admin.menu.price': 'Fiyat',
    'admin.menu.status': 'Durum',
    'admin.menu.actions': 'İşlemler',
    'admin.menu.available': 'Mevcut',
    'admin.menu.unavailable': 'Mevcut Değil',
    'admin.menu.description': 'Açıklama',
    'admin.menu.menuType': 'Menü Türü',
    'admin.menu.imageUrl': 'Resim URL',
    'admin.menu.vegetarian': 'Vejetaryen',
    'admin.menu.spicy': 'Acılı',
    'admin.menu.deleteConfirm': 'Bu menü öğesini silmek istediğinizden emin misiniz?',
    'admin.menu.saveFailed': 'Menü öğesi kaydedilemedi. Lütfen tekrar deneyin.',
    'admin.menu.deleteFailed': 'Menü öğesi silinemedi. Lütfen tekrar deneyin.',

    // Common
    'common.loading': 'Yükleniyor...',
    'common.error': 'Bir hata oluştu',
    'common.success': 'Başarılı',
    'common.name': 'Ad',
    'common.description': 'Açıklama',
    'common.price': 'Fiyat',
    'common.category': 'Kategori',
    'common.image': 'Resim',
    'common.size': 'Boyut',
    'common.small': 'Küçük',
    'common.medium': 'Orta',
    'common.large': 'Büyük',

    // Privacy Policy
    'privacy.title': 'Gizlilik Politikası',
    'privacy.description': 'Shebo\'s Burger gizlilik politikası ve kişisel veri koruma uygulamaları',
    'privacy.lastUpdated': 'Son Güncelleme',
    'privacy.section1.title': 'Giriş',
    'privacy.section1.content': 'Bu gizlilik politikası, Shebo\'s Burger olarak kişisel verilerinizi nasıl topladığımızı, kullandığımızı ve koruduğumuzu açıklar.',
    'privacy.section2.title': 'Topladığımız Bilgiler',
    'privacy.section2.content': 'Hizmetlerimizi sunabilmek için aşağıdaki bilgileri toplayabiliriz:',
    'privacy.section2.item1': 'İletişim bilgileri (ad, e-posta, telefon)',
    'privacy.section2.item2': 'Sipariş geçmişi ve tercihleri',
    'privacy.section2.item3': 'Web sitesi kullanım verileri',
    'privacy.section2.item4': 'Çerez ve benzer teknolojiler',
    'privacy.section3.title': 'Bilgilerin Kullanımı',
    'privacy.section3.content': 'Topladığımız bilgileri hizmet kalitemizi artırmak, siparişlerinizi işlemek ve size daha iyi deneyim sunmak için kullanırız.',
    'privacy.section4.title': 'Bilgi Paylaşımı',
    'privacy.section4.content': 'Kişisel bilgilerinizi üçüncü taraflarla paylaşmayız, satmayız veya kiralamayız.',
    'privacy.section5.title': 'Veri Güvenliği',
    'privacy.section5.content': 'Kişisel verilerinizi korumak için endüstri standardı güvenlik önlemleri alırız.',
    'privacy.section6.title': 'Haklarınız',
    'privacy.section6.content': 'Kişisel verilerinize erişim, düzeltme veya silme hakkına sahipsiniz.',
    'privacy.contact.title': 'İletişim',
    'privacy.contact.content': 'Gizlilik politikamız hakkında sorularınız varsa bizimle iletişime geçin:',

    // Terms of Service
    'terms.title': 'Kullanım Şartları',
    'terms.description': 'Shebo\'s Burger hizmetlerinin kullanım şartları ve koşulları',
    'terms.lastUpdated': 'Son Güncelleme',
    'terms.section1.title': 'Kabul',
    'terms.section1.content': 'Bu web sitesini kullanarak bu kullanım şartlarını kabul etmiş olursunuz.',
    'terms.section2.title': 'Hizmet Tanımı',
    'terms.section2.content': 'Shebo\'s Burger, kaliteli burger ve cafe hizmetleri sunan bir restoran zinciridir.',
    'terms.section3.title': 'Kullanıcı Sorumlulukları',
    'terms.section3.content': 'Hizmetlerimizi kullanırken aşağıdaki kurallara uymanız gerekmektedir:',
    'terms.section3.item1': 'Doğru ve güncel bilgi sağlamak',
    'terms.section3.item2': 'Yasalara ve düzenlemelere uymak',
    'terms.section3.item3': 'Diğer kullanıcıların haklarına saygı göstermek',
    'terms.section3.item4': 'Hizmetleri kötüye kullanmamak',
    'terms.section4.title': 'Sipariş ve Ödeme',
    'terms.section4.content': 'Siparişler onaylandıktan sonra bağlayıcıdır. Ödeme koşulları sipariş sırasında belirtilir.',
    'terms.section5.title': 'İptal ve İade',
    'terms.section5.content': 'İptal ve iade koşulları sipariş türüne göre değişiklik gösterebilir.',
    'terms.section6.title': 'Sorumluluk Sınırlaması',
    'terms.section6.content': 'Hizmetlerimizden kaynaklanan zararlar için sorumluluğumuz yasal sınırlarla kısıtlıdır.',
    'terms.section7.title': 'Değişiklikler',
    'terms.section7.content': 'Bu şartları önceden haber vermeksizin değiştirme hakkımızı saklı tutarız.',
    'terms.contact.title': 'İletişim',
    'terms.contact.content': 'Kullanım şartları hakkında sorularınız varsa bizimle iletişime geçin:',
  },
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.menu': 'Menu',
    'nav.blog': 'Blog',
    'nav.contact': 'Contact',
    'nav.admin': 'Admin',

    // Homepage
    'hero.title': 'Indulge in Burger Perfection',
    'hero.subtitle': 'Handcrafted burgers made with premium ingredients for an unforgettable taste experience.',
    'hero.cta': 'View Menu',
    'google.cta': 'Get Directions',

    'about.title': 'Our Story',
    'about.content': 'Shebo\'s Burger was founded in 2015 with a simple mission: create the most delicious, high-quality burgers using only the freshest ingredients. What started as a small food truck has grown into a beloved local restaurant, but our commitment to quality and flavor has never wavered.',

    'testimonials.title': 'Customer Reviews',
    'testimonials.1.text': 'Best burger in town! The ingredients are always fresh and the flavor is incredible.',
    'testimonials.1.author': 'John Smith',
    'testimonials.2.text': 'Amazing atmosphere and perfect service. Definitely recommend!',
    'testimonials.2.author': 'Sarah Johnson',
    'testimonials.3.text': 'The vegetarian options are also very delicious. A place for everyone.',
    'testimonials.3.author': 'Mike Wilson',

    'contact.title': 'Contact & Location',
    'contact.address': 'Address',
    'contact.phone': 'Phone',
    'contact.email': 'Mail',
    'contact.hours': 'Opening Hours',
    'contact.hours.weekdays': 'Monday - Friday: 11:00 - 23:00',
    'contact.hours.weekend': 'Saturday - Sunday: 10:00 - 24:00',

    // Contact Form
    'contact.form.title': 'Get in Touch',
    'contact.form.subtitle': 'Contact us for your questions, suggestions, or reservation requests.',
    'contact.form.name': 'Full Name',
    'contact.form.email': 'Email',
    'contact.form.phone': 'Phone',
    'contact.form.message': 'Message',
    'contact.form.send': 'Send Message',
    'contact.form.sending': 'Sending...',
    'contact.form.success': 'Your message has been sent successfully! We will get back to you soon.',
    'contact.form.error': 'An error occurred while sending the message. Please try again.',
    'contact.form.nameRequired': 'Full name is required',
    'contact.form.nameMinLength': 'Full name must be at least 2 characters',
    'contact.form.namePlaceholder': 'Enter your full name',
    'contact.form.emailRequired': 'Email address is required',
    'contact.form.emailInvalid': 'Please enter a valid email address',
    'contact.form.emailPlaceholder': '<EMAIL>',
    'contact.form.phoneRequired': 'Phone number is required',
    'contact.form.phoneInvalid': 'Please enter a valid phone number',
    'contact.form.phonePlaceholder': '+90 5XX XXX XX XX',
    'contact.form.messageRequired': 'Message is required',
    'contact.form.messageMinLength': 'Message must be at least 10 characters',
    'contact.form.messagePlaceholder': 'Write your message here...',
    'contact.form.recaptcha': 'This site is protected by reCAPTCHA.',

    // Menu
    'menu.title': 'Our Menu',
    'menu.featured': 'Featured Flavors',
    'menu.restaurant': 'Burger & Hotdog',
    'menu.cafe': 'Cafe',
    'menu.search': 'Search menu...',
    'menu.filter.all': 'All',
    'menu.filter.vegetarian': 'Vegetarian',
    'menu.filter.spicy': 'Spicy',
    'menu.currency': '₺',

    // Menu Categories
    'category.hamburgers': 'Hamburgers',
    'category.chicken-burgers': 'Chicken Burgers',
    'category.hot-dogs': 'Hot Dogs',
    'category.hot-coffees': 'Hot Coffees',
    'category.brew-coffees': 'Brew Coffees',
    'category.cold-coffees': 'Cold Coffees',
    'category.teas-drinks': 'Teas & Other Drinks',
    'category.snacks': 'Salty / Sweet Snacks',

    // Blog
    'blog.title': 'Blog',
    'blog.readMore': 'Read More',
    'blog.shareOn': 'Share on:',
    'blog.category': 'Category',
    'blog.author': 'Author',
    'blog.date': 'Date',

    // Footer
    'footer.about': 'About Us',
    'footer.description': 'Shebo\'s Burger has been serving the most delicious burgers with the freshest ingredients since 2015. Our commitment to quality and flavor has never wavered.',
    'footer.links': 'Quick Links',
    'footer.hours': 'Opening Hours',
    'footer.hours.weekdays': 'Monday - Friday',
    'footer.hours.saturday': 'Saturday',
    'footer.hours.sunday': 'Sunday',
    'footer.contact': 'Contact Us',
    'footer.address': 'Bostanlı District, İzmir',
    'footer.phone': '+90 232 123 45 67',
    'footer.email': '<EMAIL>',
    'footer.copyright': 'All Rights Reserved.',
    'footer.privacy': 'Privacy Policy',
    'footer.terms': 'Terms of Service',

    // Admin
    'admin.title': 'Admin Dashboard',
    'admin.login': 'Login',
    'admin.logout': 'Logout',
    'admin.email': 'Email',
    'admin.password': 'Password',
    'admin.dashboard': 'Dashboard',
    'admin.menu': 'Menu Management',
    'admin.blog': 'Blog Management',
    'admin.content': 'Content Management',
    'admin.addNew': 'Add New',
    'admin.edit': 'Edit',
    'admin.delete': 'Delete',
    'admin.save': 'Save',
    'admin.cancel': 'Cancel',
    'admin.loading': 'Loading...',
    'admin.welcome': 'Welcome to Shebo\'s Burger admin panel.',
    'admin.stats.menuItems': 'Menu Items',
    'admin.stats.blogPosts': 'Blog Posts',
    'admin.stats.contentSections': 'Content Sections',
    'admin.quickActions': 'Quick Actions',
    'admin.manageMenu': 'Manage Menu',
    'admin.manageMenu.desc': 'Add, edit, or delete menu items and categories',
    'admin.manageBlog': 'Manage Blog',
    'admin.manageBlog.desc': 'Create and edit blog posts',
    'admin.manageContent': 'Manage Content',
    'admin.manageContent.desc': 'Update homepage and other content sections',
    'admin.devTools': 'Development Tools',
    'admin.signOut': 'Sign Out',
    'admin.goTo': 'Go to',
    'admin.viewWebsite': 'View Website',
    'admin.settings': 'Settings',
    'admin.contactSubmissions': 'Contact Submissions',
    'admin.contactSubmissions.desc': 'View and manage contact form submissions',
    'admin.settings.desc': 'Manage passwords and user accounts',
    'admin.menuImportTool': 'Menu Import Tool',
    'admin.databaseTest': 'Database Test',
    'admin.changePassword': 'Change Password',
    'admin.userManagement': 'User Management',
    'admin.currentPassword': 'Current Password',
    'admin.newPassword': 'New Password',
    'admin.confirmPassword': 'Confirm Password',
    'admin.updatePassword': 'Update Password',
    'admin.createUser': 'Create User',
    'admin.role': 'Role',
    'admin.admin': 'Admin',
    'admin.editor': 'Editor',
    'admin.existingUsers': 'Existing Users',
    'admin.user': 'User',
    'admin.lastSignIn': 'Last Sign In',
    'admin.actions': 'Actions',
    'admin.never': 'Never',
    'admin.language': 'Language',
    'admin.turkish': 'Türkçe',
    'admin.english': 'English',

    // Menu Management
    'admin.menu.title': 'Menu Management',
    'admin.menu.subtitle': 'Add, edit, and delete menu items',
    'admin.menu.addItem': 'Add Item',
    'admin.menu.editItem': 'Edit Menu Item',
    'admin.menu.addNewItem': 'Add New Menu Item',
    'admin.menu.restaurant': 'Restaurant',
    'admin.menu.cafe': 'Café',
    'admin.menu.search': 'Search menu items...',
    'admin.menu.noItems': 'No menu items found. Try a different search or add a new item.',
    'admin.menu.loading': 'Loading menu items...',
    'admin.menu.name': 'Name',
    'admin.menu.category': 'Category',
    'admin.menu.price': 'Price',
    'admin.menu.status': 'Status',
    'admin.menu.actions': 'Actions',
    'admin.menu.available': 'Available',
    'admin.menu.unavailable': 'Unavailable',
    'admin.menu.description': 'Description',
    'admin.menu.menuType': 'Menu Type',
    'admin.menu.imageUrl': 'Image URL',
    'admin.menu.vegetarian': 'Vegetarian',
    'admin.menu.spicy': 'Spicy',
    'admin.menu.deleteConfirm': 'Are you sure you want to delete this menu item?',
    'admin.menu.saveFailed': 'Failed to save menu item. Please try again.',
    'admin.menu.deleteFailed': 'Failed to delete menu item. Please try again.',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'An error occurred',
    'common.success': 'Success',
    'common.name': 'Name',
    'common.description': 'Description',
    'common.price': 'Price',
    'common.category': 'Category',
    'common.image': 'Image',
    'common.size': 'Size',
    'common.small': 'Small',
    'common.medium': 'Medium',
    'common.large': 'Large',

    // Privacy Policy
    'privacy.title': 'Privacy Policy',
    'privacy.description': 'Shebo\'s Burger privacy policy and personal data protection practices',
    'privacy.lastUpdated': 'Last Updated',
    'privacy.section1.title': 'Introduction',
    'privacy.section1.content': 'This privacy policy explains how we, as Shebo\'s Burger, collect, use, and protect your personal data.',
    'privacy.section2.title': 'Information We Collect',
    'privacy.section2.content': 'To provide our services, we may collect the following information:',
    'privacy.section2.item1': 'Contact information (name, email, phone)',
    'privacy.section2.item2': 'Order history and preferences',
    'privacy.section2.item3': 'Website usage data',
    'privacy.section2.item4': 'Cookies and similar technologies',
    'privacy.section3.title': 'Use of Information',
    'privacy.section3.content': 'We use the collected information to improve our service quality, process your orders, and provide you with a better experience.',
    'privacy.section4.title': 'Information Sharing',
    'privacy.section4.content': 'We do not share, sell, or rent your personal information to third parties.',
    'privacy.section5.title': 'Data Security',
    'privacy.section5.content': 'We take industry-standard security measures to protect your personal data.',
    'privacy.section6.title': 'Your Rights',
    'privacy.section6.content': 'You have the right to access, correct, or delete your personal data.',
    'privacy.contact.title': 'Contact',
    'privacy.contact.content': 'If you have questions about our privacy policy, please contact us:',

    // Terms of Service
    'terms.title': 'Terms of Service',
    'terms.description': 'Terms and conditions for using Shebo\'s Burger services',
    'terms.lastUpdated': 'Last Updated',
    'terms.section1.title': 'Acceptance',
    'terms.section1.content': 'By using this website, you agree to these terms of service.',
    'terms.section2.title': 'Service Description',
    'terms.section2.content': 'Shebo\'s Burger is a restaurant chain providing quality burger and cafe services.',
    'terms.section3.title': 'User Responsibilities',
    'terms.section3.content': 'When using our services, you must comply with the following rules:',
    'terms.section3.item1': 'Provide accurate and current information',
    'terms.section3.item2': 'Comply with laws and regulations',
    'terms.section3.item3': 'Respect the rights of other users',
    'terms.section3.item4': 'Do not misuse the services',
    'terms.section4.title': 'Orders and Payment',
    'terms.section4.content': 'Orders are binding once confirmed. Payment terms are specified during ordering.',
    'terms.section5.title': 'Cancellation and Refunds',
    'terms.section5.content': 'Cancellation and refund conditions may vary depending on the order type.',
    'terms.section6.title': 'Limitation of Liability',
    'terms.section6.content': 'Our liability for damages arising from our services is limited by legal boundaries.',
    'terms.section7.title': 'Changes',
    'terms.section7.content': 'We reserve the right to change these terms without prior notice.',
    'terms.contact.title': 'Contact',
    'terms.contact.content': 'If you have questions about our terms of service, please contact us:',
  }
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('tr'); // Default to Turkish

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'tr' || savedLanguage === 'en')) {
      setLanguage(savedLanguage);
    }
  }, []);

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('language', lang);
  };

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
