import { createClient } from '@supabase/supabase-js';
import { productionConfig, isProduction } from '../config/production';

// Admin client with service role key for admin operations
const supabaseUrl = isProduction
  ? productionConfig.supabaseUrl
  : (import.meta.env.VITE_SUPABASE_URL || productionConfig.supabaseUrl);

const supabaseServiceKey = isProduction
  ? productionConfig.supabaseServiceRoleKey
  : (import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY || productionConfig.supabaseAnonKey);

export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Function to test admin connection
export const testAdminConnection = async () => {
  try {
    const { data, error } = await supabaseAdmin
      .from('menu_items')
      .select('count(*)')
      .limit(1);

    return { success: !error, error, data };
  } catch (err) {
    return { success: false, error: err, data: null };
  }
};
