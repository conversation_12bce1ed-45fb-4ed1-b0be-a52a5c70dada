import { createClient } from '@supabase/supabase-js';
import { productionConfig, isProduction } from '../config/production';

// Use environment variables in development, production config in production
const supabaseUrl = isProduction
  ? productionConfig.supabaseUrl
  : (import.meta.env.VITE_SUPABASE_URL || productionConfig.supabaseUrl);

const supabaseKey = isProduction
  ? productionConfig.supabaseAnonKey
  : (import.meta.env.VITE_SUPABASE_ANON_KEY || productionConfig.supabaseAnonKey);

export const supabase = createClient(supabaseUrl, supabaseKey);

// Types for database tables
export type MenuItem = {
  id: string;
  name: string;
  name_en?: string;
  description?: string;
  description_en?: string;
  price?: number;
  price_small?: number;
  price_medium?: number;
  price_large?: number;
  category: string;
  menu_type: 'cafe' | 'restaurant';
  image_url?: string;
  is_vegetarian?: boolean;
  is_spicy?: boolean;
  is_available: boolean;
  has_sizes?: boolean;
  display_order?: number;
  created_at: string;
};

export type BlogPost = {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  image_url?: string;
  author: string;
  category: string;
  slug?: string;
  published: boolean;
  created_at: string;
};

export type ContentSection = {
  id: string;
  name: string;
  title: string;
  content: string;
  image_url?: string;
  display_order: number;
  section: string;
  page: string;
};

export type ContactSubmission = {
  id: string;
  name: string;
  email: string;
  phone: string;
  message: string;
  recaptcha_score?: number;
  created_at: string;
};