import { useLanguage } from '../contexts/LanguageContext';
import SEO from '../components/SEO';

const PrivacyPolicy = () => {
  const { t } = useLanguage();

  return (
    <div className="pt-16">
      <SEO
        title={`${t('privacy.title')} - <PERSON><PERSON>'s <PERSON>`}
        description={t('privacy.description')}
        keywords="privacy policy, data protection, personal information, cookies"
      />

      <div className="section-padding bg-cream">
        <div className="container-custom max-w-4xl">
          <h1 className="text-4xl font-serif font-bold mb-8 text-center">{t('privacy.title')}</h1>
          
          <div className="prose prose-lg max-w-none">
            <p className="text-lg mb-6">{t('privacy.lastUpdated')}: {new Date().toLocaleDateString()}</p>
            
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('privacy.section1.title')}</h2>
              <p className="mb-4">{t('privacy.section1.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('privacy.section2.title')}</h2>
              <p className="mb-4">{t('privacy.section2.content')}</p>
              <ul className="list-disc pl-6 mb-4">
                <li>{t('privacy.section2.item1')}</li>
                <li>{t('privacy.section2.item2')}</li>
                <li>{t('privacy.section2.item3')}</li>
                <li>{t('privacy.section2.item4')}</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('privacy.section3.title')}</h2>
              <p className="mb-4">{t('privacy.section3.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('privacy.section4.title')}</h2>
              <p className="mb-4">{t('privacy.section4.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('privacy.section5.title')}</h2>
              <p className="mb-4">{t('privacy.section5.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('privacy.section6.title')}</h2>
              <p className="mb-4">{t('privacy.section6.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('privacy.contact.title')}</h2>
              <p className="mb-4">{t('privacy.contact.content')}</p>
              <p className="mb-2"><strong>{t('contact.email')}:</strong> <EMAIL></p>
              <p className="mb-2"><strong>{t('contact.phone')}:</strong> +90 232 123 45 67</p>
              <p><strong>{t('contact.address')}:</strong> Bostanlı Mahallesi, İzmir</p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
