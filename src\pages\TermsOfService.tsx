import { useLanguage } from '../contexts/LanguageContext';
import SEO from '../components/SEO';

const TermsOfService = () => {
  const { t } = useLanguage();

  return (
    <div className="pt-16">
      <SEO
        title={`${t('terms.title')} - <PERSON><PERSON>'s Burger`}
        description={t('terms.description')}
        keywords="terms of service, conditions, legal, restaurant terms"
      />

      <div className="section-padding bg-cream">
        <div className="container-custom max-w-4xl">
          <h1 className="text-4xl font-serif font-bold mb-8 text-center">{t('terms.title')}</h1>
          
          <div className="prose prose-lg max-w-none">
            <p className="text-lg mb-6">{t('terms.lastUpdated')}: {new Date().toLocaleDateString()}</p>
            
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('terms.section1.title')}</h2>
              <p className="mb-4">{t('terms.section1.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('terms.section2.title')}</h2>
              <p className="mb-4">{t('terms.section2.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('terms.section3.title')}</h2>
              <p className="mb-4">{t('terms.section3.content')}</p>
              <ul className="list-disc pl-6 mb-4">
                <li>{t('terms.section3.item1')}</li>
                <li>{t('terms.section3.item2')}</li>
                <li>{t('terms.section3.item3')}</li>
                <li>{t('terms.section3.item4')}</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('terms.section4.title')}</h2>
              <p className="mb-4">{t('terms.section4.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('terms.section5.title')}</h2>
              <p className="mb-4">{t('terms.section5.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('terms.section6.title')}</h2>
              <p className="mb-4">{t('terms.section6.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('terms.section7.title')}</h2>
              <p className="mb-4">{t('terms.section7.content')}</p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('terms.contact.title')}</h2>
              <p className="mb-4">{t('terms.contact.content')}</p>
              <p className="mb-2"><strong>{t('contact.email')}:</strong> <EMAIL></p>
              <p className="mb-2"><strong>{t('contact.phone')}:</strong> +90 232 123 45 67</p>
              <p><strong>{t('contact.address')}:</strong> Bostanlı Mahallesi, İzmir</p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TermsOfService;
