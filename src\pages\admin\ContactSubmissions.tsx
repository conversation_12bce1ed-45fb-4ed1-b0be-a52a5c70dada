import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Mail, Phone, User, Calendar, Star, Trash2, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { supabase, ContactSubmission } from '../../lib/supabaseClient';
import AdminLayout from '../../components/admin/AdminLayout';
import { useAdminLanguage } from '../../contexts/AdminLanguageContext';

const ContactSubmissions = () => {
  const [submissions, setSubmissions] = useState<ContactSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSubmission, setSelectedSubmission] = useState<ContactSubmission | null>(null);
  const [showModal, setShowModal] = useState(false);
  const { t } = useAdminLanguage();

  useEffect(() => {
    fetchSubmissions();
  }, []);

  const fetchSubmissions = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('contact_submissions')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSubmissions(data || []);
    } catch (error) {
      console.error('Error fetching contact submissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm(t('admin.contactSubmissions.deleteConfirm'))) {
      return;
    }

    try {
      const { error } = await supabase
        .from('contact_submissions')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setSubmissions(prev => prev.filter(submission => submission.id !== id));
    } catch (error) {
      console.error('Error deleting submission:', error);
      alert('Failed to delete submission. Please try again.');
    }
  };

  const handleView = (submission: ContactSubmission) => {
    setSelectedSubmission(submission);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedSubmission(null);
  };

  const getReCaptchaColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 0.7) return 'text-green-600';
    if (score >= 0.5) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getReCaptchaText = (score?: number) => {
    if (!score) return 'N/A';
    if (score >= 0.7) return t('admin.contactSubmissions.highTrust');
    if (score >= 0.5) return t('admin.contactSubmissions.mediumTrust');
    return t('admin.contactSubmissions.lowTrust');
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">{t('admin.contactSubmissions.title')}</h1>
        <p className="text-gray-600">{t('admin.contactSubmissions.subtitle')}</p>
      </div>

      {loading ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p>{t('admin.contactSubmissions.loading')}</p>
        </div>
      ) : submissions.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">{t('admin.contactSubmissions.noSubmissions')}</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6">
          {submissions.map((submission) => (
            <SubmissionCard
              key={submission.id}
              submission={submission}
              onView={handleView}
              onDelete={handleDelete}
              getReCaptchaColor={getReCaptchaColor}
              getReCaptchaText={getReCaptchaText}
              t={t}
            />
          ))}
        </div>
      )}

      {/* View Modal */}
      {showModal && selectedSubmission && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-2xl font-bold">{t('admin.contactSubmissions.details')}</h2>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('admin.contactSubmissions.name')}</label>
                  <p className="text-lg">{selectedSubmission.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('admin.contactSubmissions.email')}</label>
                  <p className="text-lg">
                    <a href={`mailto:${selectedSubmission.email}`} className="text-coffee hover:underline">
                      {selectedSubmission.email}
                    </a>
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('admin.contactSubmissions.phone')}</label>
                  <p className="text-lg">
                    <a href={`tel:${selectedSubmission.phone}`} className="text-coffee hover:underline">
                      {selectedSubmission.phone}
                    </a>
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('admin.contactSubmissions.submitted')}</label>
                  <p className="text-lg">{format(new Date(selectedSubmission.created_at), 'PPpp')}</p>
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">{t('admin.contactSubmissions.message')}</label>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="whitespace-pre-wrap">{selectedSubmission.message}</p>
                </div>
              </div>

              {selectedSubmission.recaptcha_score && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">{t('admin.contactSubmissions.recaptchaScore')}</label>
                  <div className="flex items-center">
                    <Star className={`h-5 w-5 mr-2 ${getReCaptchaColor(selectedSubmission.recaptcha_score)}`} />
                    <span className={`font-medium ${getReCaptchaColor(selectedSubmission.recaptcha_score)}`}>
                      {selectedSubmission.recaptcha_score.toFixed(2)} - {getReCaptchaText(selectedSubmission.recaptcha_score)}
                    </span>
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-3">
                <button
                  onClick={closeModal}
                  className="btn bg-gray-200 text-gray-800 hover:bg-gray-300"
                >
                  {t('admin.contactSubmissions.close')}
                </button>
                <button
                  onClick={() => handleDelete(selectedSubmission.id)}
                  className="btn bg-red-600 text-white hover:bg-red-700 flex items-center"
                >
                  <Trash2 className="h-5 w-5 mr-2" />
                  {t('admin.delete')}
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AdminLayout>
  );
};

const SubmissionCard = ({
  submission,
  onView,
  onDelete,
  getReCaptchaColor,
  getReCaptchaText,
  t
}: {
  submission: ContactSubmission;
  onView: (submission: ContactSubmission) => void;
  onDelete: (id: string) => void;
  getReCaptchaColor: (score?: number) => string;
  getReCaptchaText: (score?: number) => string;
  t: (key: string) => string;
}) => (
  <motion.div
    className="bg-white rounded-lg shadow-md overflow-hidden"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.4 }}
  >
    <div className="p-6">
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center">
          <User className="h-5 w-5 text-coffee mr-2" />
          <h3 className="text-xl font-bold">{submission.name}</h3>
        </div>
        <div className="flex items-center text-sm text-gray-500">
          <Calendar className="h-4 w-4 mr-1" />
          {format(new Date(submission.created_at), 'MMM d, yyyy')}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="flex items-center">
          <Mail className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm">{submission.email}</span>
        </div>
        <div className="flex items-center">
          <Phone className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm">{submission.phone}</span>
        </div>
      </div>

      <div className="mb-4">
        <p className="text-gray-600 line-clamp-3">{submission.message}</p>
      </div>

      <div className="flex justify-between items-center">
        <div className="flex items-center">
          {submission.recaptcha_score && (
            <>
              <Star className={`h-4 w-4 mr-1 ${getReCaptchaColor(submission.recaptcha_score)}`} />
              <span className={`text-sm ${getReCaptchaColor(submission.recaptcha_score)}`}>
                {getReCaptchaText(submission.recaptcha_score)}
              </span>
            </>
          )}
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={() => onView(submission)}
            className="btn bg-coffee text-white hover:bg-coffee/90 flex items-center text-sm"
          >
            <Eye className="h-4 w-4 mr-1" />
            {t('admin.contactSubmissions.view')}
          </button>
          <button
            onClick={() => onDelete(submission.id)}
            className="btn bg-red-600 text-white hover:bg-red-700 flex items-center text-sm"
          >
            <Trash2 className="h-4 w-4 mr-1" />
            {t('admin.delete')}
          </button>
        </div>
      </div>
    </div>
  </motion.div>
);

export default ContactSubmissions;
