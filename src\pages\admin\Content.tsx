import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { motion } from 'framer-motion';
import { Edit, X, Save, LayoutDashboard } from 'lucide-react';
import { supabase, ContentSection } from '../../lib/supabaseClient';
import AdminLayout from '../../components/admin/AdminLayout';
import RichTextEditor from '../../components/admin/RichTextEditor';
import MultiLanguageForm from '../../components/admin/MultiLanguageForm';
import { useAdminLanguage } from '../../contexts/AdminLanguageContext';

type FormData = Omit<ContentSection, 'id'>;

const AdminContent = () => {
  const [contentSections, setContentSections] = useState<ContentSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeSection, setActiveSection] = useState<ContentSection | null>(null);
  const [showForm, setShowForm] = useState(false);

  // Multi-language state
  const [primaryLanguage, setPrimaryLanguage] = useState<'tr' | 'en'>('tr');
  const [multiLangTitle, setMultiLangTitle] = useState({ tr: '', en: '' });
  const [multiLangContent, setMultiLangContent] = useState({ tr: '', en: '' });

  const { register, handleSubmit, reset, setValue, control, formState: { errors } } = useForm<FormData>();
  const { t } = useAdminLanguage();

  useEffect(() => {
    fetchContentSections();
  }, []);

  // Fetch content sections
  const fetchContentSections = async () => {
    setLoading(true);

    try {
      const { data, error } = await supabase
        .from('content')
        .select('*')
        .order('page')
        .order('display_order');

      if (error) throw error;

      if (data) {
        setContentSections(data);
      }
    } catch (error) {
      console.error('Error fetching content sections:', error);

      // Use default data if database fetch fails
      const defaultSections: ContentSection[] = [
        {
          id: '1',
          name: 'hero',
          title: 'Burger Mükemmelliğinin Tadını Çıkarın',
          title_en: 'Indulge in Burger Perfection',
          content: 'Unutulmaz bir lezzet deneyimi için premium malzemelerle el yapımı burgerler.',
          content_en: 'Handcrafted burgers made with premium ingredients for an unforgettable taste experience.',
          image_url: 'https://images.pexels.com/photos/1639562/pexels-photo-1639562.jpeg',
          display_order: 1,
          section: 'hero',
          page: 'home',
          language: 'tr'
        },
        {
          id: '2',
          name: 'about',
          title: 'Hikayemiz',
          title_en: 'Our Story',
          content: "Shebo's Burger 2015 yılında basit bir misyonla kuruldu: sadece en taze malzemeleri kullanarak en lezzetli, yüksek kaliteli burgerleri yaratmak. Küçük bir yemek kamyonu olarak başlayan şey, sevilen yerel bir restorana dönüştü, ancak kalite ve lezzet konusundaki bağlılığımız hiç sarsılmadı.",
          content_en: "Shebo's Burger was founded in 2015 with a simple mission: create the most delicious, high-quality burgers using only the freshest ingredients. What started as a small food truck has grown into a beloved local restaurant, but our commitment to quality and flavor has never wavered.",
          image_url: 'https://images.pexels.com/photos/2983101/pexels-photo-2983101.jpeg',
          display_order: 2,
          section: 'about',
          page: 'home',
          language: 'tr'
        }
      ];

      setContentSections(defaultSections);
    } finally {
      setLoading(false);
    }
  };

  // Handle edit form submission
  const onSubmit = async (data: FormData) => {
    try {
      if (activeSection) {
        // Prepare data with multi-language content
        const submitData = {
          ...data,
          language: primaryLanguage,
          title: multiLangTitle[primaryLanguage],
          content: multiLangContent[primaryLanguage],
          title_en: primaryLanguage === 'tr' ? multiLangTitle.en : undefined,
          content_en: primaryLanguage === 'tr' ? multiLangContent.en : undefined,
        };

        // Update existing section
        const { error } = await supabase
          .from('content')
          .update(submitData)
          .eq('id', activeSection.id);

        if (error) throw error;

        // Update local state
        setContentSections(prev =>
          prev.map(section =>
            section.id === activeSection.id
              ? { ...section, ...submitData }
              : section
          )
        );
      }

      // Reset form and close
      closeForm();
    } catch (error) {
      console.error('Error saving content section:', error);
      alert('Failed to save content section. Please try again.');
    }
  };

  // Open form for editing
  const handleEdit = (section: ContentSection) => {
    setActiveSection(section);

    // Set primary language and multi-language content
    setPrimaryLanguage(section.language || 'tr');
    setMultiLangTitle({
      tr: section.title,
      en: section.title_en || ''
    });
    setMultiLangContent({
      tr: section.content,
      en: section.content_en || ''
    });

    // Set form values
    setValue('name', section.name);
    setValue('title', section.title);
    setValue('content', section.content);
    setValue('image_url', section.image_url || '');
    setValue('display_order', section.display_order);
    setValue('section', section.section);
    setValue('page', section.page);
    setValue('language', section.language || 'tr');
    setValue('title_en', section.title_en || '');
    setValue('content_en', section.content_en || '');

    setShowForm(true);
  };

  // Close form
  const closeForm = () => {
    setShowForm(false);
    setActiveSection(null);

    // Reset multi-language state
    setPrimaryLanguage('tr');
    setMultiLangTitle({ tr: '', en: '' });
    setMultiLangContent({ tr: '', en: '' });

    reset();
  };

  // Group content sections by page
  const groupedSections: Record<string, ContentSection[]> = contentSections.reduce((acc, section) => {
    if (!acc[section.page]) {
      acc[section.page] = [];
    }
    acc[section.page].push(section);
    return acc;
  }, {} as Record<string, ContentSection[]>);

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">{t('admin.content.title')}</h1>
        <p className="text-gray-600">{t('admin.content.subtitle')}</p>
      </div>

      {loading ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p>{t('admin.content.loading')}</p>
        </div>
      ) : Object.keys(groupedSections).length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p>{t('admin.content.noSections')}</p>
        </div>
      ) : (
        Object.entries(groupedSections).map(([page, sections]) => (
          <div key={page} className="mb-8">
            <h2 className="text-2xl font-bold mb-4 capitalize">
              {t(`admin.content.page.${page}`) !== `admin.content.page.${page}` ? t(`admin.content.page.${page}`) : `${page} Page`}
            </h2>

            <div className="grid grid-cols-1 gap-6">
              {sections.map((section) => (
                <ContentCard
                  key={section.id}
                  section={section}
                  onEdit={handleEdit}
                  t={t}
                />
              ))}
            </div>
          </div>
        ))
      )}

      {/* Edit Form Modal */}
      {showForm && activeSection && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-2xl font-bold">
                {t('admin.content.editSection')}: {activeSection.name}
              </h2>
              <button
                onClick={closeForm}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="p-6">
              <div className="grid grid-cols-1 gap-6">
                {/* Multi-Language Content */}
                <MultiLanguageForm
                  title={multiLangTitle}
                  content={multiLangContent}
                  onTitleChange={(lang, value) => {
                    setMultiLangTitle(prev => ({ ...prev, [lang]: value }));
                    if (lang === primaryLanguage) {
                      setValue('title', value);
                    }
                  }}
                  onContentChange={(lang, value) => {
                    setMultiLangContent(prev => ({ ...prev, [lang]: value }));
                    if (lang === primaryLanguage) {
                      setValue('content', value);
                    }
                  }}
                  primaryLanguage={primaryLanguage}
                  onPrimaryLanguageChange={(lang) => {
                    setPrimaryLanguage(lang);
                    setValue('language', lang);
                    setValue('title', multiLangTitle[lang]);
                    setValue('content', multiLangContent[lang]);
                  }}
                  titleLabel={t('admin.content.title.field')}
                  contentLabel={t('admin.content.content.field')}
                  titlePlaceholder="Enter section title..."
                  contentPlaceholder="Write your content here..."
                />

                <div>
                  <label className="form-label">{t('admin.content.imageUrl')}</label>
                  <input
                    {...register('image_url')}
                    className="form-input"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="form-label">{t('admin.content.sectionId')}</label>
                    <input
                      {...register('section', { required: 'Section ID is required' })}
                      className="form-input"
                      placeholder="e.g., hero, about"
                      readOnly
                    />
                  </div>

                  <div>
                    <label className="form-label">{t('admin.content.page')}</label>
                    <input
                      {...register('page', { required: 'Page is required' })}
                      className="form-input"
                      placeholder="e.g., home, about"
                      readOnly
                    />
                  </div>

                  <div>
                    <label className="form-label">{t('admin.content.displayOrder')}</label>
                    <input
                      type="number"
                      {...register('display_order', {
                        required: 'Display order is required',
                        min: { value: 1, message: 'Display order must be at least 1' }
                      })}
                      className="form-input"
                      placeholder="1"
                    />
                    {errors.display_order && (
                      <p className="text-red-600 text-sm mt-1">{errors.display_order.message}</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end gap-3">
                <button
                  type="button"
                  onClick={closeForm}
                  className="btn bg-gray-200 text-gray-800 hover:bg-gray-300"
                >
                  {t('admin.cancel')}
                </button>
                <button
                  type="submit"
                  className="btn btn-primary flex items-center"
                >
                  <Save className="h-5 w-5 mr-2" />
                  {t('admin.content.saveChanges')}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AdminLayout>
  );
};

// Content Card Component
const ContentCard = ({
  section,
  onEdit,
  t
}: {
  section: ContentSection;
  onEdit: (section: ContentSection) => void;
  t: (key: string) => string;
}) => (
  <motion.div
    className="bg-white rounded-lg shadow-md overflow-hidden"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.4 }}
  >
    <div className="p-4 bg-coffee text-white flex justify-between items-center">
      <div className="flex items-center">
        <LayoutDashboard className="h-5 w-5 mr-2" />
        <span className="font-semibold capitalize">{section.name}</span>
      </div>
      <span className="text-sm bg-white text-coffee px-2 py-1 rounded-full">
        Order: {section.display_order}
      </span>
    </div>

    <div className="p-6">
      <h3 className="text-xl font-bold mb-2">{section.title}</h3>
      <p className="text-gray-600 mb-4">{section.content}</p>

      {section.image_url && (
        <div className="mb-4">
          <p className="text-sm text-gray-500 mb-2">{t('admin.content.currentImage')}:</p>
          <img
            src={section.image_url}
            alt={section.title}
            className="h-40 w-full object-cover rounded-md"
          />
        </div>
      )}

      <div className="flex justify-end">
        <button
          onClick={() => onEdit(section)}
          className="btn bg-coffee text-white hover:bg-coffee/90 flex items-center"
        >
          <Edit className="h-5 w-5 mr-2" />
          {t('admin.content.editContent')}
        </button>
      </div>
    </div>
  </motion.div>
);

export default AdminContent;