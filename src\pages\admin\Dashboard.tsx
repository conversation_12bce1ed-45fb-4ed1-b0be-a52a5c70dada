import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { supabase } from '../../lib/supabaseClient';
import AdminLayout from '../../components/admin/AdminLayout';
import SampleDataLoader from '../../components/admin/SampleDataLoader';
import DatabaseTest from '../../components/admin/DatabaseTest';
import MenuImporter from '../../components/admin/MenuImporter';
import { ArrowRight, Utensils, FileText, LayoutDashboard, Users, Mail, Settings } from 'lucide-react';
import { useAdminLanguage } from '../../contexts/AdminLanguageContext';

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    menuItems: 0,
    blogPosts: 0,
    contentSections: 0,
  });
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { t } = useAdminLanguage();

  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);

      try {
        // Get menu items count
        const { count: menuCount, error: menuError } = await supabase
          .from('menu_items')
          .select('*', { count: 'exact', head: true });

        if (menuError) throw menuError;

        // Get blog posts count
        const { count: blogCount, error: blogError } = await supabase
          .from('blog_posts')
          .select('*', { count: 'exact', head: true });

        if (blogError) throw blogError;

        // Get content sections count
        const { count: contentCount, error: contentError } = await supabase
          .from('content')
          .select('*', { count: 'exact', head: true });

        if (contentError) throw contentError;

        setStats({
          menuItems: menuCount || 0,
          blogPosts: blogCount || 0,
          contentSections: contentCount || 0,
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
        setStats({
          menuItems: 6, // Default values
          blogPosts: 3,
          contentSections: 2,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    navigate('/login');
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">{t('admin.title')}</h1>
        <p className="text-gray-600">{t('admin.welcome')}</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <StatsCard
          title={t('admin.stats.menuItems')}
          value={stats.menuItems}
          icon={<Utensils className="h-8 w-8" />}
          color="bg-green-100 text-green-800"
          loading={loading}
        />
        <StatsCard
          title={t('admin.stats.blogPosts')}
          value={stats.blogPosts}
          icon={<FileText className="h-8 w-8" />}
          color="bg-blue-100 text-blue-800"
          loading={loading}
        />
        <StatsCard
          title={t('admin.stats.contentSections')}
          value={stats.contentSections}
          icon={<LayoutDashboard className="h-8 w-8" />}
          color="bg-purple-100 text-purple-800"
          loading={loading}
        />
      </div>

      {/* Quick Actions */}
      <h2 className="text-xl font-bold mb-4">{t('admin.quickActions')}</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
        <QuickActionCard
          title={t('admin.manageMenu')}
          description={t('admin.manageMenu.desc')}
          icon={<Utensils className="h-6 w-6" />}
          link="/admin/menu"
          color="bg-coffee text-white"
        />
        <QuickActionCard
          title={t('admin.manageBlog')}
          description={t('admin.manageBlog.desc')}
          icon={<FileText className="h-6 w-6" />}
          link="/admin/blog"
          color="bg-mocha text-white"
        />
        <QuickActionCard
          title={t('admin.manageContent')}
          description={t('admin.manageContent.desc')}
          icon={<LayoutDashboard className="h-6 w-6" />}
          link="/admin/content"
          color="bg-[#2A1A0D] text-white"
        />
        <QuickActionCard
          title={t('admin.contactSubmissions')}
          description={t('admin.contactSubmissions.desc')}
          icon={<Mail className="h-6 w-6" />}
          link="/admin/contact-submissions"
          color="bg-blue-600 text-white"
        />
        <QuickActionCard
          title={t('admin.settings')}
          description={t('admin.settings.desc')}
          icon={<Settings className="h-6 w-6" />}
          link="/admin/settings"
          color="bg-gray-600 text-white"
        />
      </div>

      {/* Menu Import Tool */}
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">{t('admin.menuImportTool')}</h2>
        <MenuImporter />
      </div>

      {/* Database Test */}
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">{t('admin.databaseTest')}</h2>
        <DatabaseTest />
      </div>

      {/* Sample Data Loader */}
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">{t('admin.devTools')}</h2>
        <SampleDataLoader />
      </div>

      {/* Sign Out Button */}
      <div className="mt-12 text-center">
        <button
          onClick={handleSignOut}
          className="btn bg-red-600 text-white hover:bg-red-700"
        >
          {t('admin.signOut')}
        </button>
      </div>
    </AdminLayout>
  );
};

// Stats Card Component
const StatsCard = ({
  title,
  value,
  icon,
  color,
  loading
}: {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  loading: boolean;
}) => (
  <motion.div
    className="bg-white rounded-lg shadow-md p-6"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.4 }}
  >
    <div className="flex justify-between items-center mb-4">
      <div className={`p-3 rounded-full ${color}`}>
        {icon}
      </div>
      {loading ? (
        <div className="h-10 w-10 bg-gray-200 rounded-md animate-pulse"></div>
      ) : (
        <span className="text-3xl font-bold">{value}</span>
      )}
    </div>
    <h3 className="text-lg font-semibold">{title}</h3>
  </motion.div>
);

// Quick Action Card Component
const QuickActionCard = ({
  title,
  description,
  icon,
  link,
  color
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  link: string;
  color: string;
}) => {
  const { t } = useLanguage();

  return (
    <motion.div
      className="bg-white rounded-lg shadow-md overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      whileHover={{ y: -5 }}
    >
      <div className={`p-4 ${color}`}>
        {icon}
      </div>
      <div className="p-6">
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        <Link
          to={link}
          className="inline-flex items-center text-coffee hover:underline font-medium"
        >
          {t('admin.goTo')} {title}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Link>
      </div>
    </motion.div>
  );
};

export default AdminDashboard;