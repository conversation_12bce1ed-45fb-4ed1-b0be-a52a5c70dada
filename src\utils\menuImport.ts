import { supabase } from '../lib/supabaseClient';

// Menu data to import
export const menuData = [
  // Cafe Menu Items
  // S<PERSON><PERSON><PERSON> (Hot Coffees)
  { name: '<PERSON><PERSON><PERSON> Kahvesi', name_en: 'Turkish Coffee', description: 'Geleneksel Türk kahvesi', description_en: 'Traditional Turkish coffee', price: 120, category: 'Sıcak Kahveler', menu_type: 'cafe', is_available: true, display_order: 1 },
  { name: '<PERSON><PERSON><PERSON> Kah<PERSON>', name_en: 'Filter Coffee', description: 'Taze çekilmiş filtre kahve', description_en: 'Freshly ground filter coffee', price: 150, category: '<PERSON><PERSON><PERSON><PERSON>hvel<PERSON>', menu_type: 'cafe', is_available: true, display_order: 2 },
  { name: 'Espresso', name_en: 'Espresso', description: 'İtalyan usulü espresso', description_en: 'Italian style espresso', price: null, price_small: 120, price_large: 130, has_sizes: true, category: '<PERSON><PERSON><PERSON><PERSON>', menu_type: 'cafe', is_available: true, display_order: 3 },
  { name: 'Americano', name_en: 'Americano', description: 'Espresso ve sıcak su karışımı', description_en: 'Espresso with hot water', price: 150, category: 'Sıcak Kahveler', menu_type: 'cafe', is_available: true, display_order: 4 },
  { name: 'Cortado', name_en: 'Cortado', description: 'Espresso ve az miktarda süt', description_en: 'Espresso with small amount of milk', price: 155, category: 'Sıcak Kahveler', menu_type: 'cafe', is_available: true, display_order: 5 },
  { name: 'Flat White', name_en: 'Flat White', description: 'Güçlü espresso ve mikroköpük süt', description_en: 'Strong espresso with microfoam milk', price: 155, category: 'Sıcak Kahveler', menu_type: 'cafe', is_available: true, display_order: 6 },
  { name: 'Cappuccino', name_en: 'Cappuccino', description: 'Espresso, süt ve süt köpüğü', description_en: 'Espresso, milk and milk foam', price: 155, category: 'Sıcak Kahveler', menu_type: 'cafe', is_available: true, display_order: 7 },
  { name: 'Cafe Latte', name_en: 'Cafe Latte', description: 'Espresso ve bol miktarda süt', description_en: 'Espresso with plenty of milk', price: 160, category: 'Sıcak Kahveler', menu_type: 'cafe', is_available: true, display_order: 8 },
  { name: 'Mocha', name_en: 'Mocha', description: 'Espresso, çikolata ve süt', description_en: 'Espresso, chocolate and milk', price: 170, category: 'Sıcak Kahveler', menu_type: 'cafe', is_available: true, display_order: 9 },
  { name: 'White Chocolate Mocha', name_en: 'White Chocolate Mocha', description: 'Beyaz çikolatalı mocha', description_en: 'White chocolate mocha', price: 170, category: 'Sıcak Kahveler', menu_type: 'cafe', is_available: true, display_order: 10 },

  // Demleme Kahveler (Brew Coffees)
  { name: 'Nitelikli Kahveler', name_en: 'Quality Coffees', description: 'Özel seçilmiş nitelikli kahve çekirdekleri', description_en: 'Specially selected quality coffee beans', price: 170, category: 'Demleme Kahveler', menu_type: 'cafe', is_available: true, display_order: 11 },
  { name: 'Mikrolot Kahveler', name_en: 'Microlot Coffees', description: 'Sınırlı üretim mikrolot kahveler', description_en: 'Limited production microlot coffees', price: 190, category: 'Demleme Kahveler', menu_type: 'cafe', is_available: true, display_order: 12 },
  { name: 'Yarışma Serisi Kahveler', name_en: 'Competition Series Coffees', description: 'Dünya çapında ödüllü kahveler (230-450₺ arası)', description_en: 'World award-winning coffees (230-450₺ range)', price: 340, category: 'Demleme Kahveler', menu_type: 'cafe', is_available: true, display_order: 13 },

  // Soğuk Kahveler (Cold Coffees)
  { name: 'Cold Brew No:1', name_en: 'Cold Brew No:1', description: 'Soğuk demleme kahve özel karışım', description_en: 'Cold brew coffee special blend', price: 180, category: 'Soğuk Kahveler', menu_type: 'cafe', is_available: true, display_order: 14 },
  { name: 'Cold Brew No:2', name_en: 'Cold Brew No:2', description: 'Premium soğuk demleme kahve', description_en: 'Premium cold brew coffee', price: 190, category: 'Soğuk Kahveler', menu_type: 'cafe', is_available: true, display_order: 15 },
  { name: 'Iced Americano', name_en: 'Iced Americano', description: 'Buzlu americano', description_en: 'Iced americano', price: 160, category: 'Soğuk Kahveler', menu_type: 'cafe', is_available: true, display_order: 16 },
  { name: 'Iced Latte', name_en: 'Iced Latte', description: 'Buzlu latte', description_en: 'Iced latte', price: 170, category: 'Soğuk Kahveler', menu_type: 'cafe', is_available: true, display_order: 17 },
  { name: 'Iced Flat White', name_en: 'Iced Flat White', description: 'Buzlu flat white', description_en: 'Iced flat white', price: 165, category: 'Soğuk Kahveler', menu_type: 'cafe', is_available: true, display_order: 18 },
  { name: 'Iced Mocha', name_en: 'Iced Mocha', description: 'Buzlu mocha', description_en: 'Iced mocha', price: 180, category: 'Soğuk Kahveler', menu_type: 'cafe', is_available: true, display_order: 19 },

  // Restaurant Menu Items
  // Hamburgerler (Hamburgers)
  { name: 'Classic Burger', name_en: 'Classic Burger', description: 'Klasik hamburger', description_en: 'Classic hamburger', price: 350, category: 'Hamburgerler', menu_type: 'restaurant', is_available: true, display_order: 50 },
  { name: 'Cheese Burger', name_en: 'Cheese Burger', description: 'Peynirli hamburger', description_en: 'Cheese hamburger', price: 360, category: 'Hamburgerler', menu_type: 'restaurant', is_available: true, display_order: 51 },
  { name: 'Trüflüm Burger', name_en: 'Truffle Burger', description: 'Trüf soslu özel burger', description_en: 'Special burger with truffle sauce', price: 365, category: 'Hamburgerler', menu_type: 'restaurant', is_available: true, display_order: 52 },
  { name: 'Barbekü Burger', name_en: 'BBQ Burger', description: 'Barbekü soslu burger', description_en: 'BBQ sauce burger', price: 365, category: 'Hamburgerler', menu_type: 'restaurant', is_available: true, display_order: 53 },
  { name: 'Sebzelim Burger', name_en: 'Veggie Burger', description: 'Sebzeli burger', description_en: 'Vegetable burger', price: 325, category: 'Hamburgerler', menu_type: 'restaurant', is_available: true, display_order: 54, is_vegetarian: true },
  { name: 'Hottaste Burger', name_en: 'Hottaste Burger', description: 'Acılı özel burger', description_en: 'Spicy special burger', price: 365, category: 'Hamburgerler', menu_type: 'restaurant', is_available: true, display_order: 55, is_spicy: true },
];

export async function importMenuItems() {
  try {
    console.log('Starting menu import...');
    
    // Insert all menu items
    const { data, error } = await supabase
      .from('menu_items')
      .insert(menuData);

    if (error) {
      console.error('Error importing menu:', error);
      return { success: false, error: error.message };
    }

    console.log('Menu imported successfully!', data);
    return { success: true, count: menuData.length };
  } catch (error) {
    console.error('Import failed:', error);
    return { success: false, error: error.message };
  }
}

export async function clearMenuItems() {
  try {
    const { error } = await supabase
      .from('menu_items')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all items

    if (error) {
      console.error('Error clearing menu:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Clear failed:', error);
    return { success: false, error: error.message };
  }
}
